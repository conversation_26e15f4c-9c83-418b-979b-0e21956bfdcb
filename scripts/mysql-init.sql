-- MySQL initialization script for <PERSON><PERSON> application
-- This script sets up the database with proper charset and collation

-- Set charset and collation for the database
ALTER DATABASE itrain CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a basic health check table if it doesn't exist
CREATE TABLE IF NOT EXISTS health_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status VARCHAR(50) NOT NULL DEFAULT 'healthy',
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    message TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial health check record
INSERT INTO health_checks (status, message) VALUES ('healthy', 'Database initialized successfully');

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON itrain.* TO 'itrain'@'%';
FLUSH PRIVILEGES;
