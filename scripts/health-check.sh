#!/bin/sh

# Health Check Script for Laravel API
# This script performs comprehensive health checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL=${API_BASE_URL:-http://web}

echo_info() {
    echo "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo "${RED}[ERROR]${NC} $1"
}

# Function to check service health
check_service_health() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}

    echo_info "Checking $service_name health..."

    response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" "$url" 2>/dev/null || echo "HTTPSTATUS:000;TIME:0")
    http_status=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    response_time=$(echo "$response" | grep -o "TIME:[0-9.]*" | cut -d: -f2)

    if [ "$http_status" = "$expected_status" ]; then
        echo_success "✓ $service_name is healthy (Status: $http_status, Time: ${response_time}s)"
        return 0
    else
        echo_error "✗ $service_name is unhealthy (Status: $http_status, Time: ${response_time}s)"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    echo_info "Checking database connectivity..."

    # Try to access a simple endpoint that would require database
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$API_BASE_URL/api/ping/health" 2>/dev/null || echo "HTTPSTATUS:000")
    http_status=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)

    if [ "$http_status" = "200" ]; then
        echo_success "✓ Database connectivity appears healthy"
        return 0
    else
        echo_error "✗ Database connectivity issues detected"
        return 1
    fi
}

# Function to check application logs for errors
check_application_logs() {
    echo_info "Checking for recent application errors..."

    # This would typically check log files, but in Docker we'll check response patterns
    response=$(curl -s "$API_BASE_URL/api/ping/health" 2>/dev/null || echo "")

    if echo "$response" | grep -q "error\|Error\|ERROR" 2>/dev/null; then
        echo_warning "⚠ Potential errors detected in application responses"
        return 1
    else
        echo_success "✓ No obvious errors detected in responses"
        return 0
    fi
}

# Function to check response times
check_performance() {
    echo_info "Checking application performance..."

    local total_time=0
    local requests=3
    local threshold=2.0

    for i in $(seq 1 $requests); do
        response=$(curl -s -w "TIME:%{time_total}" "$API_BASE_URL/api/ping/health" 2>/dev/null || echo "TIME:999")
        time=$(echo "$response" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
        total_time=$(echo "$total_time + $time" | bc 2>/dev/null || echo "999")
    done

    avg_time=$(echo "scale=3; $total_time / $requests" | bc 2>/dev/null || echo "999")

    if [ "$(echo "$avg_time < $threshold" | bc 2>/dev/null || echo "0")" = "1" ]; then
        echo_success "✓ Performance is good (Avg response time: ${avg_time}s)"
        return 0
    else
        echo_warning "⚠ Performance may be slow (Avg response time: ${avg_time}s)"
        return 1
    fi
}

# Main health check execution
main() {
    echo_info "=== Laravel API Health Check ==="
    echo_info "Starting comprehensive health checks..."

    # Health check results tracking
    total_checks=0
    passed_checks=0

    # Check 1: Basic service health
    total_checks=$((total_checks + 1))
    if check_service_health "Web Service" "$API_BASE_URL/api/ping/health"; then
        passed_checks=$((passed_checks + 1))
    fi

    # Check 2: Database connectivity
    total_checks=$((total_checks + 1))
    if check_database; then
        passed_checks=$((passed_checks + 1))
    fi

    # Check 3: Application logs
    total_checks=$((total_checks + 1))
    if check_application_logs; then
        passed_checks=$((passed_checks + 1))
    fi

    # Check 4: Performance
    total_checks=$((total_checks + 1))
    if check_performance; then
        passed_checks=$((passed_checks + 1))
    fi

    # Summary
    echo_info "\n=== Health Check Summary ==="
    echo_info "Total checks: $total_checks"
    echo_info "Passed: $passed_checks"
    echo_info "Failed: $((total_checks - passed_checks))"

    # Calculate health percentage
    health_percentage=$(echo "scale=0; $passed_checks * 100 / $total_checks" | bc 2>/dev/null || echo "0")

    if [ "$health_percentage" -ge "100" ]; then
        echo_success "🎉 System is fully healthy ($health_percentage%)"
        exit 0
    elif [ "$health_percentage" -ge "75" ]; then
        echo_warning "⚠ System is mostly healthy ($health_percentage%) but has some issues"
        exit 0
    else
        echo_error "❌ System health is poor ($health_percentage%) - immediate attention required"
        exit 1
    fi
}

# Run the main function
main
