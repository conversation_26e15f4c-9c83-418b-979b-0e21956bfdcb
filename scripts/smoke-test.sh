#!/bin/sh

# Smoke Test Script for Laravel API
# This script tests critical endpoints to ensure the application is working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL=${API_BASE_URL:-http://web}
TEST_ENDPOINT=${TEST_ENDPOINT:-/api/school_events/all?page=1&per_page=10}
EXTERNAL_URL=${EXTERNAL_URL:-https://mcne-staging.iclass.hk}
MAX_RETRIES=30
RETRY_DELAY=10

echo_info() {
    echo "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo "${RED}[ERROR]${NC} $1"
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local retries=0

    echo_info "Waiting for $service_name to be ready..."

    while [ $retries -lt $MAX_RETRIES ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo_success "$service_name is ready!"
            return 0
        fi

        retries=$((retries + 1))
        echo_info "Attempt $retries/$MAX_RETRIES: $service_name not ready yet, waiting ${RETRY_DELAY}s..."
        sleep $RETRY_DELAY
    done

    echo_error "$service_name failed to become ready after $MAX_RETRIES attempts"
    return 1
}

# Function to test an endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}

    echo_info "Testing: $description"
    echo_info "URL: $url"

    # Make the request and capture response
    response=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" "$url" || echo "HTTPSTATUS:000;TIME:0")

    # Extract status code and response time
    http_status=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    response_time=$(echo "$response" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*;TIME:[0-9.]*$//')

    # Check status code
    if [ "$http_status" = "$expected_status" ]; then
        echo_success "✓ Status: $http_status (Expected: $expected_status)"
        echo_success "✓ Response time: ${response_time}s"

        # Additional checks for JSON responses
        if echo "$body" | grep -q "^{" 2>/dev/null; then
            echo_success "✓ Valid JSON response"

            # Check for common error indicators
            if echo "$body" | grep -q '"error"' 2>/dev/null; then
                echo_warning "⚠ Response contains error field"
                echo "$body" | head -c 200
            fi
        fi

        return 0
    else
        echo_error "✗ Status: $http_status (Expected: $expected_status)"
        echo_error "✗ Response time: ${response_time}s"
        if [ -n "$body" ]; then
            echo_error "Response body (first 500 chars):"
            echo "$body" | head -c 500
        fi
        return 1
    fi
}

# Main smoke test execution
main() {
    echo_info "=== Laravel API Smoke Test ==="
    echo_info "Starting smoke tests..."

    # Wait for the web service to be ready
    if ! wait_for_service "$API_BASE_URL/api/ping/health" "Web Service"; then
        echo_error "Web service is not ready, aborting tests"
        exit 1
    fi

    # Test results tracking
    total_tests=0
    passed_tests=0

    echo_info "\n=== Running Smoke Tests ==="

    # Test 1: Health check endpoint
    total_tests=$((total_tests + 1))
    if test_endpoint "$API_BASE_URL/api/ping/health" "Health Check Endpoint"; then
        passed_tests=$((passed_tests + 1))
    fi

    # Test 2: Main test endpoint (school events)
    total_tests=$((total_tests + 1))
    if test_endpoint "$API_BASE_URL$TEST_ENDPOINT" "School Events API Endpoint"; then
        passed_tests=$((passed_tests + 1))
    fi

    # Test 3: External staging endpoint (if accessible)
    if [ -n "$EXTERNAL_URL" ]; then
        total_tests=$((total_tests + 1))
        echo_info "\n--- Testing External Staging Environment ---"
        if test_endpoint "$EXTERNAL_URL$TEST_ENDPOINT" "External Staging Endpoint"; then
            passed_tests=$((passed_tests + 1))
        fi
    fi

    # Test 4: Basic API structure endpoints
    total_tests=$((total_tests + 1))
    if test_endpoint "$API_BASE_URL/api" "API Root Endpoint" "404"; then
        passed_tests=$((passed_tests + 1))
    fi

    # Summary
    echo_info "\n=== Test Summary ==="
    echo_info "Total tests: $total_tests"
    echo_info "Passed: $passed_tests"
    echo_info "Failed: $((total_tests - passed_tests))"

    if [ $passed_tests -eq $total_tests ]; then
        echo_success "🎉 All smoke tests passed!"
        exit 0
    else
        echo_error "❌ Some tests failed. Check the logs above."
        exit 1
    fi
}

# Run the main function
main
