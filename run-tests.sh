#!/bin/bash

# Comprehensive Test Runner for Laravel API
# This script orchestrates the entire testing process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.test.yml"
PROJECT_NAME="itrain-test"

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_header() {
    echo -e "\n${BOLD}${BLUE}=== $1 ===${NC}\n"
}

# Function to clean up resources
cleanup() {
    echo_info "Cleaning up resources..."
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" down -v --remove-orphans 2>/dev/null || true
    docker system prune -f --volumes 2>/dev/null || true
}

# Function to check prerequisites
check_prerequisites() {
    echo_header "Checking Prerequisites"

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        echo_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    echo_success "✓ Docker is running"

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        echo_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    echo_success "✓ docker-compose is available"

    # Check if required files exist
    local required_files=("$COMPOSE_FILE" ".env.test" "scripts/smoke-test.sh" "scripts/health-check.sh")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            echo_error "Required file $file is missing"
            exit 1
        fi
    done
    echo_success "✓ All required files are present"
}

# Function to build and start services
start_services() {
    echo_header "Building and Starting Services"

    # Set environment variables
    export DEV=true
    export PHP_VERSION=8.3
    export DB_DRIVER=mysql
    export WEB_PORT=8081

    echo_info "Building Docker images..."
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" build --no-cache; then
        echo_error "Failed to build Docker images"
        return 1
    fi
    echo_success "✓ Docker images built successfully"

    echo_info "Starting services..."
    if ! docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" up -d; then
        echo_error "Failed to start services"
        return 1
    fi
    echo_success "✓ Services started successfully"

    # Wait for services to be healthy
    echo_info "Waiting for services to be healthy..."
    local max_wait=300  # 5 minutes
    local wait_time=0

    while [ $wait_time -lt $max_wait ]; do
        if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps | grep -q "healthy"; then
            echo_success "✓ Services are healthy"
            return 0
        fi

        echo_info "Waiting for services to be ready... (${wait_time}s/${max_wait}s)"
        sleep 10
        wait_time=$((wait_time + 10))
    done

    echo_error "Services failed to become healthy within $max_wait seconds"
    echo_info "Service status:"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" ps
    return 1
}

# Function to run database migrations
run_migrations() {
    echo_header "Running Database Migrations"

    echo_info "Running migrations..."
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" run --rm migrate; then
        echo_success "✓ Migrations completed successfully"
        return 0
    else
        echo_warning "⚠ Migrations failed or not needed"
        return 0  # Don't fail the entire test suite for migration issues
    fi
}

# Function to run smoke tests
run_smoke_tests() {
    echo_header "Running Smoke Tests"

    echo_info "Executing smoke tests..."
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" run --rm smoke-test; then
        echo_success "✓ Smoke tests passed"
        return 0
    else
        echo_error "✗ Smoke tests failed"
        return 1
    fi
}

# Function to run health checks
run_health_checks() {
    echo_header "Running Health Checks"

    echo_info "Executing health checks..."
    if docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" run --rm health-check; then
        echo_success "✓ Health checks passed"
        return 0
    else
        echo_error "✗ Health checks failed"
        return 1
    fi
}

# Function to show service logs
show_logs() {
    echo_header "Service Logs"

    echo_info "Web service logs:"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs --tail=50 web

    echo_info "Database service logs:"
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" logs --tail=20 database
}

# Function to run all tests
run_all_tests() {
    local test_results=()

    # Run migrations
    if run_migrations; then
        test_results+=("migrations:PASS")
    else
        test_results+=("migrations:WARN")
    fi

    # Run smoke tests
    if run_smoke_tests; then
        test_results+=("smoke-tests:PASS")
    else
        test_results+=("smoke-tests:FAIL")
    fi

    # Run health checks
    if run_health_checks; then
        test_results+=("health-checks:PASS")
    else
        test_results+=("health-checks:FAIL")
    fi

    # Display results
    echo_header "Test Results Summary"
    local total_tests=0
    local passed_tests=0
    local failed_tests=0

    for result in "${test_results[@]}"; do
        local test_name=$(echo "$result" | cut -d: -f1)
        local test_status=$(echo "$result" | cut -d: -f2)

        total_tests=$((total_tests + 1))

        case $test_status in
            "PASS")
                echo_success "✓ $test_name"
                passed_tests=$((passed_tests + 1))
                ;;
            "WARN")
                echo_warning "⚠ $test_name"
                passed_tests=$((passed_tests + 1))
                ;;
            "FAIL")
                echo_error "✗ $test_name"
                failed_tests=$((failed_tests + 1))
                ;;
        esac
    done

    echo_info "\nSummary:"
    echo_info "Total tests: $total_tests"
    echo_info "Passed: $passed_tests"
    echo_info "Failed: $failed_tests"

    if [ $failed_tests -eq 0 ]; then
        echo_success "\n🎉 All tests completed successfully!"
        return 0
    else
        echo_error "\n❌ Some tests failed. Check the logs above for details."
        return 1
    fi
}

# Main execution
main() {
    echo_header "Laravel API Test Suite"
    echo_info "Starting comprehensive testing process..."

    # Set trap for cleanup on exit
    trap cleanup EXIT

    # Check prerequisites
    if ! check_prerequisites; then
        echo_error "Prerequisites check failed"
        exit 1
    fi

    # Clean up any existing resources
    cleanup

    # Start services
    if ! start_services; then
        echo_error "Failed to start services"
        show_logs
        exit 1
    fi

    # Run all tests
    if run_all_tests; then
        echo_success "Test suite completed successfully!"
        exit 0
    else
        echo_error "Test suite failed!"
        show_logs
        exit 1
    fi
}

# Handle command line arguments
case "${1:-all}" in
    "prerequisites")
        check_prerequisites
        ;;
    "start")
        check_prerequisites
        cleanup
        start_services
        ;;
    "smoke")
        run_smoke_tests
        ;;
    "health")
        run_health_checks
        ;;
    "logs")
        show_logs
        ;;
    "cleanup")
        cleanup
        ;;
    "all"|*)
        main
        ;;
esac
