version: '3.8'

services:
  # Database service
  database:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-yourStrong(!)Password}
      MYSQL_DATABASE: ${DB_DATABASE:-itrain}
      MYSQL_USER: ${DB_USERNAME:-itrain}
      MYSQL_PASSWORD: ${DB_PASSWORD:-Passw0rd}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD:-yourStrong(!)Password}"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s
    networks:
      - app-network

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5
      interval: 5s
    networks:
      - app-network

  # Main web application
  web: &itrain-api
    build:
      context: .
      args:
        database_driver: ${DB_DRIVER:-mysql}
        dev: ${DEV:-true}
        php_version: ${PHP_VERSION:-8.3}
      dockerfile: Dockerfile
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - storage:/var/www/storage
      - public_storage:/var/www/public/storage
      - ./:/var/www:cached
    environment: &itrain-environment
      CONTAINER_ROLE: web
      APP_ENV: ${APP_ENV:-local}
      APP_DEBUG: ${APP_DEBUG:-true}
      APP_KEY: ${APP_KEY:-base64:CttuCzUlKpAHwjNrd8UoBVJLsv3mD+HPNxc2BsWTHRI=}
      APP_URL: ${APP_URL:-http://localhost}
      DB_HOST: database
      DB_PORT: 3306
      DB_DATABASE: ${DB_DATABASE:-itrain}
      DB_USERNAME: ${DB_USERNAME:-itrain}
      DB_PASSWORD: ${DB_PASSWORD:-Passw0rd}
      DB_DRIVER: ${DB_DRIVER:-mysql}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      CACHE_DRIVER: redis
      SESSION_DRIVER: redis
      QUEUE_CONNECTION: redis
      LOG_CHANNEL: stderr
      CTF_ENCRYPTION: false
    env_file:
      - .env.test
    ports:
      - "${WEB_PORT:-8081}:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/ping/health"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 60s
    networks:
      - app-network

  # Migration service
  migrate:
    <<: *itrain-api
    environment:
      <<: *itrain-environment
      CONTAINER_ROLE: migrate
    depends_on:
      database:
        condition: service_healthy
    ports: []
    healthcheck:
      disable: true

  # Smoke test service
  smoke-test:
    image: curlimages/curl:latest
    depends_on:
      web:
        condition: service_healthy
    volumes:
      - ./scripts/smoke-test.sh:/smoke-test.sh:ro
    command: ["/bin/sh", "/smoke-test.sh"]
    environment:
      API_BASE_URL: http://web
      TEST_ENDPOINT: /api/school_events/all?page=1&per_page=10
      EXTERNAL_URL: https://mcne-staging.iclass.hk
    networks:
      - app-network

  # Health check service
  health-check:
    image: curlimages/curl:latest
    depends_on:
      web:
        condition: service_healthy
    volumes:
      - ./scripts/health-check.sh:/health-check.sh:ro
    command: ["/bin/sh", "/health-check.sh"]
    environment:
      API_BASE_URL: http://web
    networks:
      - app-network

volumes:
  mysql_data:
  storage:
  public_storage:

networks:
  app-network:
    driver: bridge
