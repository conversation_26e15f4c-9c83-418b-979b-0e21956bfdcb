# set php version
ARG php_version=8.3

FROM php:${php_version}-apache-bookworm

# set build arguments
# set database_driver to mysql or sqlsrv to build the image only consisting of the specified database driver, by default both are built
ARG database_driver

# set to false to disable development tools (including xdebug, phpunit, etc)
ARG dev

# set default shell (not really needed, for convenience only)
RUN chsh --shell /bin/bash www-data

# set supported locales
RUN /bin/echo -e 'en_HK.UTF-8 UTF-8\nen_GB.UTF-8 UTF-8\nen_US.UTF-8 UTF-8\nzh_CN.UTF-8 UTF-8\nzh_HK.UTF-8 UTF-8\n' > /etc/locale.gen

# add repositories & php dependencies
RUN apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get -y --no-install-recommends install \
    apt-transport-https fontconfig git gnupg iproute2 \
    locales sudo unzip xfonts-75dpi xfonts-base zip wget \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get -y --no-install-recommends install \
        libmagickwand-dev \
        libjpeg62-turbo-dev \
        libgmp-dev \
        libxext6 \
        libxrender1 \
        libzip-dev \
        libzip4 \
    && savedAptMark="$(apt-mark showmanual)" \
    && docker-php-ext-install gd gmp intl xml zip \
    && pecl install imagick \
    && docker-php-ext-enable imagick \
    && apt-mark auto '.*' > /dev/null; \
  	[ -z "$savedAptMark" ] || apt-mark manual $savedAptMark \
  	&& find /usr/local -type f -executable -exec ldd '{}' ';' \
  		| awk '/=>/ { print $(NF-1) }' \
  		| sort -u \
  		| xargs -r dpkg-query --search \
  		| cut -d: -f1 \
  		| sort -u \
  		| xargs -r apt-mark manual \
  	\
  	&& apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false \
    && rm -rf /var/lib/apt/lists/*

# bcmath extension (zip and imagick already installed above)
RUN docker-php-ext-install bcmath

# add yt-dlp
RUN curl -L https://github.com/yt-dlp/yt-dlp/releases/download/2023.12.30/yt-dlp -o /usr/local/bin/yt-dlp \
    && chmod a+rx /usr/local/bin/yt-dlp \
    && rm -rf /var/lib/apt/lists/*

RUN wget https://github.com/wkhtmltopdf/packaging/releases/download/********-3/wkhtmltox_********-3.bookworm_amd64.deb
RUN dpkg -i wkhtmltox_********-3.bookworm_amd64.deb

# add ffmpeg without x11
COPY --from=mwader/static-ffmpeg:4.4.1 /ffmpeg /usr/local/bin/
COPY --from=mwader/static-ffmpeg:4.4.1 /ffprobe /usr/local/bin/
COPY --from=mwader/static-ffmpeg:4.4.1 /qt-faststart /usr/local/bin/

RUN if [ -n "$dev" ]; then \
    mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini" \
    && yes | pecl install xdebug \
    && docker-php-ext-enable xdebug \
    && echo xdebug.remote_enable=1 >> $PHP_INI_DIR/conf.d/xdebug.ini \
    && echo xdebug.remote_host=host.docker.internal >> $PHP_INI_DIR/conf.d/xdebug.ini \
    && rm -rf /var/lib/apt/lists/* \
    ; else mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini" \
    ; fi

RUN if [ -z "$database_driver" ] || [ "$database_driver" = "mysql" ]; then \
    docker-php-ext-install pdo pdo_mysql \
    ; fi

#add php.ini config
RUN sed -i 's/upload_max_filesize = 2M/upload_max_filesize = 1G/g' $PHP_INI_DIR/php.ini
RUN sed -i 's/post_max_size = 8M/post_max_size = 1G/g' $PHP_INI_DIR/php.ini

RUN if [ -z "$database_driver" ] || [ "$database_driver" = "sqlsrv" ]; then \
    curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/12/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive ACCEPT_EULA=Y apt-get -y install \
    msodbcsql17 mssql-tools unixodbc-dev \
    && ln -s /opt/mssql-tools/bin/bcp /opt/mssql-tools/bin/sqlcmd /usr/local/bin \
    && pecl install sqlsrv pdo_sqlsrv \
    && docker-php-ext-enable sqlsrv pdo_sqlsrv \
    && apt-get purge -y unixodbc-dev \
    && apt autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    ; fi

RUN a2enmod rewrite headers

# copy config into the image
COPY scripts/docker_apache_site $APACHE_CONFDIR/sites-available/000-default.conf

# install composer
COPY --from=composer:2 /usr/bin/composer /usr/local/bin/composer

# run composer before copying the rest of the file
COPY --chown=www-data:www-data ./composer.* /var/www/
WORKDIR /var/www

# Install composer dependencies
RUN if [ -n "$dev" ]; then \
       composer install --no-autoloader || composer update --no-autoloader ; \
    else \
       composer install --no-autoloader --no-dev || composer update --no-autoloader --no-dev ; \
    fi \
    && chown -R www-data:www-data vendor && rm -rf /root/.composer

# copy the rest of the file
COPY --chown=www-data:www-data . /var/www

# Finish composer dumps
RUN sudo -u www-data composer dump-autoload --optimize

# copy font files
RUN mkdir /usr/share/fonts/chinese/TrueType -p
COPY public/css/fonts/NotoSansCJK-Regular.ttc /usr/share/fonts/chinese/TrueType/

# copy php optimzation files based on development flags
RUN if [ -n "$dev" ]; then cat scripts/php-opcache.dev.ini >> $PHP_INI_DIR/conf.d/opcache.ini ; \
    else cat scripts/php-opcache.prod.ini >> $PHP_INI_DIR/conf.d/opcache.ini \
    ; fi

# set up ownerships and permissions
# RUN chmod -R g+ws /var/www

EXPOSE 80
VOLUME /var/www/storage
VOLUME /var/www/public/storage
RUN chown -R www-data:www-data /var/www/storage
RUN chown -R www-data:www-data /var/www/public/storage

RUN chmod +x /var/www/artisan

COPY ./scripts/docker_start.bash /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# USER www-data
ENTRYPOINT [ "/usr/local/bin/entrypoint.sh" ]
