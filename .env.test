# Test Environment Configuration
# This file contains environment variables for testing

### Application configuration
APP_ENV=testing
APP_DEBUG=true
APP_URL=http://localhost:8080
APP_TIMEZONE=Asia/Hong_Kong
APP_LOCALE=en
APP_KEY=base64:CttuCzUlKpAHwjNrd8UoBVJLsv3mD+HPNxc2BsWTHRI=

# Disable time limits for testing
TIME_LIMIT=0
REPORT_TIME_LIMIT=0
DB_TIME_LIMIT=0

# JSON formatting for debugging
API_JSON_PRETTYPRINT=true

### Database configuration
DB_DRIVER=mysql
DB_HOST=database
DB_PORT=3306
DB_DATABASE=itrain
DB_USERNAME=itrain
DB_PASSWORD=Passw0rd
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
MYSQL_TIMEZONE="+08:00"

### Cache and Session configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
REDIS_HOST=redis
REDIS_PORT=6379

### Authentication configuration
USER_NAME_FIELD=external_id
MAXIMUM_LOGIN_ATTEMPTS=10
PASSWORD_RULE=App\Rules\AlwaysValidPassword
APPROVE_USER=false
AUTO_APPROVE_UNIT=true

### JWT configuration
JWT_TTL=60
JWT_REFRESH_TTL=20160

### iTrain configuration
CTF_ENCRYPTION=false
ENABLE_CMS=true
ENABLE_TEACHER_CMS=true
DEFAULT_APP_ID=itw-itrain-test

### Logging configuration
LOG_CHANNEL=stderr
LOG_OAUTH2=false
LOG_HTTP=true
LOG_DATABASE=true

### Mail configuration (disabled for testing)
MAIL_DRIVER=log
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=iTrain Test

### Storage configuration
PUBLIC_DISK=public
STORAGE_PATH=/var/www/storage
STORAGE_ROOT_URL=/storage

### Trusted Proxy Configuration
TRUSTED_PROXY_PROXIES=*

### Push notification (disabled for testing)
TEST_PUSH_NOTIFICATION=false
IOS_PUSHER=App\Notifications\Push\LogPusher
ANDROID_PUSHER=App\Notifications\Push\LogPusher

### Queue configuration
TEST_QUEUE=true

### Sentry (disabled for testing)
SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=0
SENTRY_REQUEST_BODY_SIZE=none
