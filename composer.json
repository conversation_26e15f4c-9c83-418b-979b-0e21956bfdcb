{"name": "iclass/ctf-base-api", "description": "iTrain", "keywords": ["laravel", "api", "jwt", "auth", "rest"], "license": "none", "type": "project", "require": {"php": "^8.2", "ext-curl": "*", "ext-fileinfo": "*", "ext-gmp": "*", "ext-imagick": "*", "ext-intl": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-zip": "*", "aporat/store-receipt-validator": "^4.0", "apple/apn-push": "^3.0", "aws/aws-php-sns-message-validator": "^1.6", "aws/aws-sdk-php-laravel": "^3.10", "bacon/bacon-qr-code": "2.0.3", "barryvdh/laravel-ide-helper": "^3.0", "barryvdh/laravel-snappy": "^1.0", "cweagans/composer-patches": "^1.7.0", "doctrine/dbal": "^3.0", "fakerphp/faker": "^1.20", "guzzlehttp/guzzle": "^7.0.1", "h4cc/wkhtmltoimage-amd64": "0.12.x", "h4cc/wkhtmltopdf-amd64": "dev-master#4144bccbfaa40e8150a763e2d55dfedce9d13909", "illuminated/console-mutex": "^12.0", "jenssegers/agent": "^2.6", "kalnoy/nestedset": "^6.0", "kkomelin/laravel-translatable-string-exporter": "^1.0", "kreait/firebase-php": "^6.9", "laminas/laminas-diactoros": "^3.6", "laravel-lang/lang": "~7.0", "laravel/cashier": "^15.0", "laravel/framework": "^12.0", "laravel/legacy-factories": "^1.1", "laravel/tinker": "^2.0", "league/flysystem-sftp-v3": "^3.0", "league/oauth2-client": "^2.6", "norkunas/youtube-dl-php": "^2.1.2", "openspout/openspout": "^4.30", "paragonie/ecc": "^2.0", "phpoffice/phpspreadsheet": "^1.15", "phpseclib/phpseclib": "^3.0", "pragmarx/google2fa-laravel": "^2.3", "predis/predis": "^2.0", "propaganistas/laravel-phone": "^5.3.6", "riverslei/baidu-pusher": "^1.0", "silber/bouncer": "^1.0.3", "smalot/pdfparser": "*", "staudenmeir/eloquent-param-limit-fix": "^1.0", "stripe/stripe-php": "^16.2", "symfony/clock": "*", "symfony/psr-http-message-bridge": "^7.0", "thecodingmachine/safe": "^1.1", "tymon/jwt-auth": "^2.0", "web-token/jwt-key-mgmt": "^2.0.0", "web-token/jwt-signature": "^2.0.0", "wemersonjanuario/wkhtmltopdf-windows": "********"}, "suggest": {}, "require-dev": {"dxw/phar-install": "^1.1", "estahn/phpunit-json-assertions": "^4.0", "filp/whoops": "~2.0", "khanamiryan/qrcode-detector-decoder": "^1.0", "mockery/mockery": "~1.0", "neronmoon/scriptsdev": "^0.1.8", "nunomaduro/collision": "^8.8", "phpunit/phpunit": "^11.0", "symfony/css-selector": "^3.1.10", "symfony/dom-crawler": "^3.1.10"}, "autoload": {"classmap": ["database/seeds", "database/migrations"], "files": ["app/helpers.php", "phpqrcode.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Test\\": "tests/"}}, "extra": {"laravel": {"dont-discover": ["beyondcode/laravel-credentials", "tymon/jwt-auth", "barryvdh/laravel-ide-helper"]}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate", "@php artisan jwt:generate", "@php artisan server-key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "process-timeout": 0, "allow-plugins": {"cweagans/composer-patches": true, "neronmoon/scriptsdev": true}}, "minimum-stability": "dev", "prefer-stable": true}