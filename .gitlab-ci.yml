image: docker:stable

variables:
  APP_ENV: testing
  GIT_SUBMODULE_STRATEGY: normal
  PHP_VERSION: "8.3"
  TEST_PUSH_NOTIFICATION: "1"
  TEST_QUEUE: "1"

  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  SONAR_HOST_URL: https://sonarqube.iclass.hk


stages:
  - lint
  - build
  - test
  - deploy

before_script:
  - export IMAGE_TAG=mcne-php$PHP_VERSION-$DB_DRIVER
  - cp .env.gitlab .env



sonarqube-check:
  image:
    name: sonarsource/sonar-scanner-cli:11
    entrypoint: [""]
  stage: lint
  script:
    - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
  allow_failure: true
  only:
    - feat/deploy-mcne-to-k8s-laravel-12

build_docker_mariadb:
  stage: build
  variables:
    COMPOSE_PROJECT_NAME: itrain-mariadb
    DB_DRIVER: mysql
    DB_VENDOR: mariadb
  image: &build-image
    name: gcr.io/kaniko-project/executor:v1.24.0-debug

    entrypoint: [""]
  script: &build-script
      - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > /kaniko/.docker/config.json
      # Pre-pull base images to ensure they're available
      - crane pull php:$PHP_VERSION-apache-bookworm || true
      - crane pull composer:2 || true
      - crane pull mwader/static-ffmpeg:4.4.1 || true
      - |
        /kaniko/executor \
        --build-arg database_driver=$DB_DRIVER \
        --build-arg dev=false \
        --build-arg php_version=$PHP_VERSION \
        --cache=true \
        --force \
        --context $CI_PROJECT_DIR \
        --dockerfile $CI_PROJECT_DIR/Dockerfile \
        --destination "$CI_REGISTRY_IMAGE:$IMAGE_TAG-$CI_COMMIT_SHORT_SHA" \
        --destination "$CI_REGISTRY_IMAGE:$IMAGE_TAG-latest"

.test-before-script: &test-before-script
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $CI_REGISTRY_IMAGE:$IMAGE_TAG-$CI_COMMIT_SHORT_SHA || true
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml up -d database
  - sleep 30 # wait for DB to come up

.test-after-script: &test-after-script
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml run --rm -T web /var/www/artisan migrate -v
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml run --rm -T web /var/www/artisan migrate:rollback -v
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml run --rm -T web /var/www/artisan migrate -v
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml run --rm -T web /var/www/artisan db:seed -v
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml up -d
  - docker-compose -f docker-compose.yml -f docker-compose-$DB_VENDOR.yml run --rm -T web /var/www/vendor/bin/phpunit -c /var/www/phpunit.xml

test_docker_mariadb:
  stage: test
  variables:
    COMPOSE_PROJECT_NAME: itrain-mariadb
    DB_DRIVER: mysql
    DB_VENDOR: mariadb
  allow_failure: true
  script:
    - *test-before-script
    - *test-after-script

deploy-mysql-k8s-staging:
  image: docker:24
  stage: deploy
  needs: ["test_docker_mariadb"]
  variables:
    COMPOSE_PROJECT_NAME: itrain-mariadb
    DB_DRIVER: mysql
    DB_VENDOR: mariadb
  script:
    - mkdir -p ~/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > ~/.docker/config.json
    - docker buildx imagetools create $CI_REGISTRY_IMAGE:$IMAGE_TAG-$CI_COMMIT_SHORT_SHA --tag $CI_REGISTRY_IMAGE/$IMAGE_TAG/$CI_ENVIRONMENT_NAME:$CI_PIPELINE_ID
  environment:
    name: staging
  only:
    - feat/deploy-mcne-to-k8s-laravel-12


# deploy-mysql-k8s-uat:
#   image: docker:24
#   stage: deploy
#   needs: ["test_docker_mariadb"]
#   variables:
#     COMPOSE_PROJECT_NAME: itrain-mariadb
#     DB_DRIVER: mysql
#     DB_VENDOR: mariadb
#   script:
#     - mkdir -p ~/.docker
#     - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > ~/.docker/config.json
#     - docker buildx imagetools create $CI_REGISTRY_IMAGE:$IMAGE_TAG-$CI_COMMIT_SHORT_SHA --tag $CI_REGISTRY_IMAGE/$IMAGE_TAG/$CI_ENVIRONMENT_NAME:$CI_PIPELINE_ID
#   environment:
#     name: uat
#   when: manual
#   only:
#     - feat/deploy-mcne-to-k8s-laravel-12

# deploy-mysql-k8s-prod:
#   image: docker:24
#   stage: deploy
#   needs: ["test_docker_mariadb"]
#   variables:
#     COMPOSE_PROJECT_NAME: itrain-mariadb
#     DB_DRIVER: mysql
#     DB_VENDOR: mariadb
#   script:
#     - mkdir -p ~/.docker
#     - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > ~/.docker/config.json
#     - docker buildx imagetools create $CI_REGISTRY_IMAGE:$IMAGE_TAG-$CI_COMMIT_SHORT_SHA --tag $CI_REGISTRY_IMAGE/$IMAGE_TAG/$CI_ENVIRONMENT_NAME:$CI_PIPELINE_ID
#   environment:
#     name: production
#   when: manual
#   only:
#     - feat/deploy-mcne-to-k8s-laravel-12

