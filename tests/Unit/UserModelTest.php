<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable;
use Illuminate\Contracts\Auth\CanResetPassword;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

class UserModelTest extends TestCase
{
    public function test_user_class_exists()
    {
        $this->assertTrue(class_exists(User::class));
    }

    public function test_user_implements_required_interfaces()
    {
        $reflection = new ReflectionClass(User::class);
        
        $this->assertTrue($reflection->implementsInterface(Authenticatable::class));
        $this->assertTrue($reflection->implementsInterface(Authorizable::class));
        $this->assertTrue($reflection->implementsInterface(CanResetPassword::class));
    }

    public function test_user_has_required_methods()
    {
        $reflection = new ReflectionClass(User::class);
        
        $requiredMethods = [
            'getAuthIdentifierName',
            'getAuthIdentifier',
            'getAuthPassword',
            'getRememberToken',
            'setRememberToken',
            'getRememberTokenName',
            'getEmailForPasswordReset',
            'sendPasswordResetNotification',
        ];

        foreach ($requiredMethods as $method) {
            $this->assertTrue(
                $reflection->hasMethod($method),
                "User class should have method: {$method}"
            );
        }
    }

    public function test_user_has_required_properties()
    {
        $reflection = new ReflectionClass(User::class);
        
        $this->assertTrue($reflection->hasProperty('fillable'));
        $this->assertTrue($reflection->hasProperty('hidden'));
        $this->assertTrue($reflection->hasProperty('casts'));
    }

    public function test_user_extends_correct_base_class()
    {
        $reflection = new ReflectionClass(User::class);
        $parentClass = $reflection->getParentClass();
        
        $this->assertNotFalse($parentClass);
        $this->assertEquals(
            'App\\OverridingLibraries\\Illuminate\\Database\\Eloquent\\Model',
            $parentClass->getName()
        );
    }

    public function test_user_uses_required_traits()
    {
        $reflection = new ReflectionClass(User::class);
        $traits = $reflection->getTraitNames();
        
        $requiredTraits = [
            'Illuminate\\Auth\\Authenticatable',
            'Illuminate\\Foundation\\Auth\\Access\\Authorizable',
            'Illuminate\\Auth\\Passwords\\CanResetPassword',
            'Illuminate\\Database\\Eloquent\\Factories\\HasFactory',
            'Illuminate\\Notifications\\Notifiable',
        ];

        foreach ($requiredTraits as $trait) {
            $this->assertContains(
                $trait,
                $traits,
                "User class should use trait: {$trait}"
            );
        }
    }

    public function test_user_table_name()
    {
        $reflection = new ReflectionClass(User::class);
        
        if ($reflection->hasProperty('table')) {
            $tableProperty = $reflection->getProperty('table');
            $tableProperty->setAccessible(true);
            $defaultValue = $tableProperty->getValue(new User());
            
            // 如果沒有明確設置 table 屬性，Laravel 會使用類名的複數形式
            // 對於 User 類，預設應該是 'users'
            $this->assertNull($defaultValue, 'Table name should use Laravel default (users)');
        }
    }
}
