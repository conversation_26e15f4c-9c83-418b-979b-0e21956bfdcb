<?php

declare(strict_types=1);

namespace App\Http\Controllers\Models\LearningCms;

use App\Exceptions\RegisterException;
use App\Http\Controllers\FilterTrait;
use App\Http\Controllers\Models\UserController as Base;
use App\Http\Controllers\RecordPageAccess;
use App\Models\Ability;
use App\Models\Department;
use App\Models\Learning\CmsCourse as Course;
use App\Models\Learning\Subject;
use App\Models\Learning\Theme;
use App\Models\Pivots\PermissionPivot;
use App\Models\Role;
use App\Models\SimpleUser;
use App\Models\Unit;
use App\Models\User;
use App\Models\RejectedUser;
use App\Models\TrainerInformation;
use App\OverridingLibraries\Illuminate\Database\Eloquent\Model;
use App\Jobs\send_password_reset_email;
use App\Scopes\UnitScope;
use App\Scopes\ViewScope;
use App\Auth\TokenRepository;
use Carbon\Carbon;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Contracts\Filesystem\Cloud;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Silber\Bouncer\Bouncer;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Laminas\Diactoros\Response\EmptyResponse;
use function array_diff;
use function array_key_exists;
use function array_reduce;
use function json_decode;
use function nullable;
use Exception;

/**
 * @Resource("User CMS", uri="/learning_cms/users")
 */
class UserController extends Base
{
    use FilterTrait;
    use RecordPageAccess;

    const TEMPLATE_PATH = "trainer_registrations/";

    private $cache;

    private $storage;

    private $bouncer;
    /**
     * @var ConnectionInterface
     */
    private $connection;

    public function __construct(
        User $userStub,
        SimpleUser $simpleUserStub,
        Repository $cache,
        Bouncer $bouncer,
        ConnectionInterface $connection,
        Hasher $hasher
    ) {
        parent::__construct($userStub, $simpleUserStub, $hasher);
        $this->cache = $cache;
        $this->bouncer = $bouncer;
        $this->connection = $connection;
    }

    /**
     * List all users
     *
     * Arbitrary field can be used as filter, e.g. id, department_id, title, phone, etc.
     *
     * @Get("/{?name,type,page,per_page,id,department_id,title,department_ids,agent_title,  upline_code}")
     * @Parameters({
     *     @Parameter("name"),
     *     @Parameter("type", required=false, description="only returns users of specific type"),
     *     @Parameter("id"),
     *     @Parameter("department_id"),
     *     @Parameter("title"),
     *     @Parameter("page", type="int", example=1),
     *     @Parameter("per_page", type="int", example=100),
     *     @Parameter("department_ids", type="string", description="a JSON array of department ids"),
     *     @Parameter("has_rights", type="boolean", description="select only users with privileges and/or permissions"),
     * })
     *
     * @param Request $request
     * @return mixed
     */
    public function index(Request $request)
    {
        ($this->userStub)::serializing(
            function (User $user) {
                $user->setHidden(array_diff($user->getHidden(), ['nid', 'email']));
            }
        );
        if ($request->query('unit_id') !== null) {
            $this->authorize('create_system_admin');
            UnitScope::$unit = Unit::findOrFail($request->query('unit_id'));
        }

        $query = $this->userStub->CurrentUserViewable()->select(['users.*',]);

        if ($query->getModel()->hasGlobalScope(ViewScope::class)) {
            /** @var ViewScope $scope */
            $scope = $query->getModel()->getGlobalScope(ViewScope::class);
            $query->addSelect($scope->getAlias($query->getModel()) . '.*');
        }
        $filter_data = Arr::except(
            $request->query(),
            [
                'password',
                'remember_token',
                'type',
                'secret',
                'name',
                'page',
                'per_page',
                'department_id',
                'department_ids',
                'only_admin',
                'has_rights',
                'has_no_rights',
                'roles',
                'abilities',
                'created_at_from',
                'created_at_to',
                'entry_date_from',
                'entry_date_to',
                'nid',
                'email',
                'first_name',
                'last_name',
                'full_name',
                'external_id',
                'unapproved',
                'approved'
            ]
        );
        $this->filterRequestArguments(
            $query,
            $filter_data
        );
        foreach (
            [
                'users.created_at' => ['created_at_from', 'created_at_to'],
                'users.entry_date' => ['entry_date_from', 'entry_date_to']
            ] as $field => [$from_field, $to_field]
        ) {
            nullable(
                $request->query($from_field),
                static function (string $from_value) use ($field, $query) {
                    $query->where($field, '>=', $from_value);
                }
            );
            nullable(
                $request->query($to_field),
                static function (string $to_value) use ($field, $query) {
                    $query->where($field, '<=', $to_value);
                }
            );
        }
        foreach (['name', 'email', 'nid', 'first_name', 'last_name', 'full_name', 'external_id'] as $field) {
            $input = $request->query($field);
            if ($input) {
                $wrapped_input = Arr::wrap($input);
                $query->where(
                    function (Builder $builder) use ($wrapped_input, $field) {
                        foreach ($wrapped_input as $item) {
                            $builder->orWhere('users.' . $field, 'like', '%' . $item . '%');
                        }
                    }
                );
            }
        }
        nullable(
            $request->query('department_ids'),
            function (string $department_ids_json) use ($query) {
                $query->whereIn(
                    'users.department_id',
                    function (QueryBuilder $q) use ($department_ids_json) {
                        $q->select('id')->from('departments');
                        foreach (json_decode($department_ids_json, true) as $department_id) {
                            $department = Department::findOrFail($department_id);
                            $q->orWhereBetween('_lft', [$department->_lft, $department->_rgt]);
                        }
                    }
                );
            }
        );
        if ($request->query('only_admin')) {
            // get only admin users
            $query->where(
                function (Builder $query) {
                    $query->whereIn(
                        'users.id',
                        function (QueryBuilder $query) {
                            $query->from('assigned_roles')->select('entity_id')->whereIn(
                                'role_id',
                                function (QueryBuilder $query) {
                                    $query->from('roles')->select('id')->where('name', 'like', '%_admin%');
                                }
                            );
                        }
                    );
                }
            )
                ->with(['roles' => function ($role) {
                    $role->select('id', 'name', 'title');
                }]);
        }

        if ($request->query('has_rights')) {
            $query->where(
                function (Builder $query) {
                    $query->whereIn(
                        'users.id',
                        function (QueryBuilder $query) {
                            $query->from('assigned_roles')->select('entity_id')->where('entity_type', User::class);
                        }
                    );
                }
            )
                ->with(['roles' => function ($role) {
                    $role->select('id', 'name', 'title');
                }]);
        }
        if ($request->query('has_no_rights')) {
            $query->where(
                function (Builder $query) {
                    $query->whereNotIn(
                        'users.id',
                        function (QueryBuilder $query) {
                            $query->from('assigned_roles')->select('entity_id')->where('entity_type', User::class);
                        }
                    );
                }
            );
        }
        if ($request->query('unapproved')) {
            $query->whereNull('users.approved_at');
        } else {
            if ($request->query('approved')) {
                $query->whereNotNull('users.approved_at');
            }
        }
        if ($request->query('page') !== null) {
            return $query->paginate();
        }

        $collection = new Collection();
        $query->chunk(
            1000,
            function (Collection $chunk) use (&$collection) {
                $collection = $collection->merge($chunk);
            }
        );
        $collection->each(
            function (User $user) {
                $user->setHidden(array_diff($user->getHidden(), ['nid']));
            }
        );
        return $collection;
    }

    public function show(Request $request, User $user)
    {
        ($this->userStub)::serializing(
            function (User $user) {
                $user->setHidden(array_diff($user->getHidden(), ['nid']));
            }
        );
        $user->load([
            'subjectPermissions',
            'themePermissions',
            'coursePermissions',
            'currentApprovedSchoolRegistrationMember.schoolRegistration',
        ]);

        return parent::show($request, $user);
    }

    /**
     * Create a user
     *
     * @Post("/")
     * @Request({
     *     "external_id" : "123",
     *     "name" : "Test User",
     *     "entry_date" : "2017-11-20",
     *     "department_id" : "123",
     *     "title" : "Teacher",
     *     "division" : "AB unit",
     * })
     *
     * @param Request $request
     * @param Department $departmentStub
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function store(Request $request, Department $departmentStub)
    {
        $this->authorize('create', User::class);

        $data = $request->validate([
            'external_id' => 'required|string',
            'unit_external_id' => 'nullable',
            'name' => 'required|string',
            'password' => 'required|string',
            'email' => 'nullable|email:rfc',
        ]);

        // check if unit exists
        $unit_id = $request->input('unit_id');
        if ($unit_id !== null) {
            $this->authorize('create_system_admin');
            $unit = Unit::find($unit_id);
            if (!$unit) {
                throw new UnprocessableEntityHttpException('Unit does not exist');
            }
        } else {
            $unit = Unit::getDefault();
        }

        UnitScope::$unit = $unit;

        if ($this->userStub->whereExternalId($request->input('external_id') ?? '')->count()) {
            throw new UnprocessableEntityHttpException('User ID is duplicated');
        }

        if (strpos($data['external_id'], '@') !== false) {
            throw new RegisterException(__('external id contains invalid character @'));
        }

        // check if email exists
        $email = $request->input('email');
        if (!empty($email) && User::withoutGlobalScopes()->where('email', $email)->exists()) {
            throw new UnprocessableEntityHttpException('Email has already been used');
        }
        // check if department_id exists
        $department_id = $request->input('department_id');
        if (!empty($department_id) && !$departmentStub->find($department_id)) {
            throw new UnprocessableEntityHttpException('Department does not exist.');
        }
        // check if supervisor_id exists
        $supervisor_id = $request->input('supervisor_id');
        if (!empty($supervisor_id) && !$this->userStub->findOrFail($supervisor_id)) {
            throw new UnprocessableEntityHttpException('Supervisor does not exist.');
        }

        // check if unit have enough seats
        if (!Unit::isDefault($unit) && $unit->seat_size < $unit->memberCount() + 1) {
            throw new UnprocessableEntityHttpException("have not enough seats for all users");
        }

        $external_id = $request->input('external_id');
        $supervisor = null;
        $upline_code = $request->input('upline_code');
        $upline_name_zh = null;
        $upline_name_en = null;
        if ($upline_code === null || empty($upline_code)) {
            if ($request->input('agent_title') == 'Pre-recruit' || $request->input('agent_title') == 'Recruit') {
                throw new UnprocessableEntityHttpException('Agent Upline Code is required.');
            }
        } else {
            $supervisor = User::where('external_id', $upline_code)->first();

            if (!is_null($supervisor)) {
                $upline_name_zh = $supervisor->name_zh;
                $upline_name_en = $supervisor->name;
                if ($request->input('agent_title') == 'Pre-recruit' || $request->input('agent_title') == 'Recruit') {
                    User::where('external_id', $upline_code)->increment('ac_num', 1);
                    $currentNumber = sprintf('%04u', $supervisor->ac_num);
                    $external_id = $upline_code . $currentNumber;
                }
            }
        }
        /** @var User $user */
        User::unguarded(
            function () use ($unit, $request, &$user, $department_id, $external_id, $supervisor, $upline_name_zh, $upline_name_en) {
                /** @var User $user */
                $user = User::make(
                    [
                        'entry_date' => $request->json('entry_date') ?? new Carbon(),
                        'department_id' => !empty($department_id) ? $department_id : null,
                        'unit_id' => $unit->id,
                        'external_id' => $external_id,
                        'user_lft' => 0,
                        'user_rgt' => 0,
                    ] + $request->json()->all()
                );
                if ($user->agent_title == 'pre-recruit' || $user->agent_title == 'Pre-recruit' || $request->input('agent_title') == 'Recruit') {
                    $user->password_reset = 0;
                    $user->expiry = date('Y-m-d H:i:s', strtotime('+6 months'));
                    $user->password = null;
                } else {
                    $user->password_reset = 0;
                }
                if ($user->external_id === null) {
                    $user->external_id = User::generateNewId(null, 8, 'external_id');
                }
                $user->id = User::generateNewId();
                $user->approved_at = Carbon::now();
                $user->save();
                if (!is_null($supervisor)) {
                    $user->supervisor_id = $supervisor->id;
                }
                $user->save();
                if ($user->agent_title == "DM" || $user->agent_title == "SDM" || $user->agent_title == "BM" || $user->agent_title == "SBM" || $user->agent_title == "DD" || $user->agent_title == "SDD" || $user->agent_title == "EDD") {
                    $user->manager_id = 1;
                }
                $user->save();
                if ($user->agent_title == 'pre-recruit' || $user->agent_title == 'Pre-recruit' || $request->input('agent_title') == 'Recruit') {
                    $resetpassword_class = app(TokenRepository::class)->create($user);
                    send_password_reset_email::dispatch($user, $resetpassword_class)->onQueue('emails');
                }
                // vod does not require email verification
                $user->email_verified_at = Carbon::now();
                $user->save();
            }
        );

        $this->recordPageAccess('users.create', $this->connection, null, $user, $request->getContent());
        $this->addSubjectAbilitiesByUser($user, $user->department_id, Ability::VIEW_SUBJECT);

        return new JsonResponse(
            $user->toArray(),
            Response::HTTP_CREATED
        );

        //todo: set as queue job
        $this->setupNestedSet();
    }

    /**
     * Update a user
     *
     * @Patch("/{id}")
     * @Request({"email" : "<EMAIL>", "phone" : "12345678", "division" : "AB unit"})
     * @Parameters({
     * })
     * @param Request $request
     * @param string $id
     * @return EmptyResponse
     * @throws AuthorizationException
     */
    public function update(Request $request, string $id)
    {
        if ($request->getMethod() !== 'PATCH') {
            throw new MethodNotAllowedHttpException(['GET', 'PATCH']);
        }

        $data = $request->json()->all();

        $user = $this->userStub->findOrFail($id);
        $this->authorize('update', $user);
        // update subject ability for department user
        if ($user->department_id !== $data['department_id']) {
            // check if still have permission from managing departments after changing own department
            $managing_departments = $user->viewableDepartments;
            $original_department = $user->department;
            $overlapped = false;
            foreach ($managing_departments as $parent) {
                if ($original_department->isSelfOrDescendantOf($parent)) {
                    $overlapped = true;
                    break;
                }
            }
            if (!$overlapped) {
                $this->removeSubjectAbilitiesByUser($user, $user->department_id, Ability::VIEW_SUBJECT);
            }
            if (isset($data['department_id'])) {
                $this->addSubjectAbilitiesByUser($user, $data['department_id'], Ability::VIEW_SUBJECT);
            }
        }
        Model::unguarded(function () use ($user, $data) {
            // check if email changed
            $updateEmail = false;
            $email = isset($data['email']) ? $data['email'] : null;
            if ($email && $email !== $user->email) {
                $updateEmail = true;
                if (User::withoutGlobalScopes()->where('email', $email)->exists()) {
                    throw new UnprocessableEntityHttpException('Email has already been used');
                }
            }
            $user->fill($data);
            if ($updateEmail) {
                $user->email_verified_at = null;
                $user->email_token = null;
            }
            $user->save();
            if (!is_null($user->upline_code)) {
                $supervisor = User::where('external_id', $user->upline_code)->first();
                if (!is_null($supervisor)) {
                    $user->supervisor_id = $supervisor->id;
                }
            }
            if ($user->agent_title == "DM" || $user->agent_title == "SDM" || $user->agent_title == "BM" || $user->agent_title == "SBM" || $user->agent_title == "DD" || $user->agent_title == "SDD" || $user->agent_title == "EDD") {
                $user->manager_id = 1;
            }
            $user->save();
        });
        $this->recordPageAccess('users.update', $this->connection, null, $user, $request->getContent());

        return new EmptyResponse();
    }

    /**
     * update users' locked
     *
     * @POST("/locked_status")
     * @Request([{"id": "xxxxx", "locked": false}, {"id": "xxxx", "locked": true}])
     * @Parameters({
     * })
     * @param Request $request
     * @param string $id
     * @return EmptyResponse
     * @throws AuthorizationException
     */
    public function updateLockedStatus(Request $request)
    {

        $this->authorize('update', User::class);
        $inputs = new Collection($request->all());
        $output_list = array("success" => [], "fail" => []);
        foreach ($inputs as $input) {
            $user = User::find($input['id']);
            if (!is_null($user) && $this->authorize('update', $user)) {
                $user->locked = $input['locked'] ? 1 : 0;
                if ($input['locked']) {
                    $user->locked_reason = isset($input['reason']) ? $input['reason'] : '';
                }
                $user->save();
                array_push($output_list['success'], $input['id']);
            } else {
                array_push($output_list['fail'], $input['id']);
            }
        }
        return $output_list;
    }

    /**
     * Find a user with the external id
     *
     * @Get("/users/find")
     * @Request({})
     * @Parameters({
     * })
     * @param Request $request
     * @param string $id
     * @return EmptyResponse
     * @throws AuthorizationException
     */
    public function findWithoutUnit(Request $request)
    {
        $count = User::withoutGlobalScopes()->whereExternalId($request->query('external_id') ?? '')->count();

        if ($count <= 0) {
            return json_encode(['result' => false]);
        } else {
            return json_encode(['result' => true]);
        }
    }

    /**
     * Get user titles
     *
     * @Get("/titles")
     * @Parameters({
     *     @Parameter("title"),
     *     @Parameter("page", type="int", example=1),
     *     @Parameter("per_page", type="int", example=100),
     * })
     * @param Request $request
     * @return mixed
     */
    public function getTitles(Request $request)
    {
        return $this->getFieldItems($request, 'title');
    }

    public function getDivisions(Request $request)
    {
        return $this->getFieldItems($request, 'division');
    }

    public function getAgentTitles(Request $request)
    {
        return $this->getFieldItems($request, 'agent_title');
    }

    public function getDistricts(Request $request)
    {
        return $this->getFieldItems($request, 'district');
    }

    /**
     * get cached version of users (only active non-expiring users)
     *
     * @Get("/cached")
     *
     * @param Request $request
     * @return mixed
     */
    public function showCached(Request $request)
    {
        return $this->cache->get('learning_cms_users', function () {
            $query = $this->userStub->onlyCurrent()->with(['department' => function (Relation $relation) {
                $relation->select('id', 'unit_id', 'long_description', 'short_description');
            }])->select([
                'id',
                'name',
                'department_id',
                'title',
            ]);
            $collection = new Collection();
            $query->chunk(
                1000,
                function (Collection $chunk) use (&$collection) {
                    $collection = $collection->merge($chunk);
                }
            );
            $this->cache->put(
                'learning_cms_users',
                $collection->toJson(),
                (new Carbon)->addHours(21)->setTime(7, 59, 0)
            );
            return $collection;
        });
    }

    /**
     * Shows currently available privileges for selection
     *
     * Note that this list is not per-user, but over- availability of privileges.
     *
     * @Get("/privileges")
     * @Response(200, body={"PRIVILEGE_MODERATE_DISCUSSIONS": 1})
     * @return array
     */
    public function showPrivileges()
    {
        $result = [];
        /** @noinspection SuspiciousLoopInspection */
        for ($bit = 1; $bit > 0; $bit <<= 1) {
            $string = User::getPrivilegeString($bit);
            if (Str::startsWith($string, 'PRIVILEGE_')) {
                $result[$string] = $bit;
            }
        }
        return $result;
    }

    /**
     * Set the privileges permissions of the user
     *
     * Only users with PRIVILEGE_CHANGE_USERS_PRIVILEGES privilege can use this API
     *
     * The elements at the root level of the request are optional.
     * If they are not specified, they will be left as-is.
     * However, if they are specified, the content inside will be cleared.
     * E.g. if user permissions are specified, all user permissions of that course are replaced.
     * This allows removal of permissions without using a separate API.
     *
     * True means allow and false means deny.
     *
     * Note that existing permissions are completely removed, so you must put it in whole (hence PUT request.)
     *
     * @Put("/{user}/permissions")
     * @Parameters({
     *     @Parameter("user", required=true, type="int"),
     * })
     * @Request(
     *     {
     *          "privileges" : 127,
     *          "subject_permissions" : {
     *              "1234" : 15,
     *              "5678" : 15,
     *          },
     *          "theme_permissions" : {
     *              "1234" : 15,
     *              "5678" : 15,
     *          },
     *          "course_permissions" : {
     *              "1234" : 15,
     *              "5678" : 15,
     *          },
     *      }
     * )
     *
     * @param Request $request
     * @param User $user
     */
    public function setPermissions(Request $request, User $user)
    {
        $this->authorize('updatePermissions', $user);
        $data = $request->json()->all();
        $this->recordPageAccess(
            $request->route()->getName(),
            $this->connection,
            $request->header('X-Ctf-App-Id'),
            $user,
            json_encode($data)
        );
        if (isset($data['privileges'])) {
            $user->privileges = $data['privileges'];
            $user->save();
        }
        $viewable_themes = [];
        $viewable_subjects = [];

        // remove all permissions first
        $user->coursePermissions()->detach();
        $user->themePermissions()->detach();
        $user->subjectPermissions()->detach();
        if (isset($data['course_permissions'])) {
            foreach ($data['course_permissions'] as $id => $permission) {
                /** @var Course $unit */
                $unit = Course::findOrFail($id);
                $viewable_themes[$unit->theme->getKey()] = true;
                $unit->userAccesses()->save($user, ['permission' => $permission]);
            }
        }
        if (isset($data['theme_permissions'])) {
            foreach ($data['theme_permissions'] as $id => $permission) {
                /** @var Theme $unit */
                $unit = Theme::findOrFail($id);
                unset($viewable_themes[$id]);
                $viewable_subjects[$unit->subject->getKey()] = true;
                $unit->userAccesses()->save($user, ['permission' => $permission]);
            }
            foreach ($viewable_themes as $key => $value) {
                $unit = Theme::findOrFail($key);
                $viewable_subjects[$unit->subject->getKey()] = true;
                $unit->userAccesses()->save($user, ['permission' => PermissionPivot::READ]);
            }
        }
        if (isset($data['subject_permissions'])) {
            foreach ($data['subject_permissions'] as $id => $permission) {
                /** @var Subject $unit */
                $unit = Subject::findOrFail($id);
                unset($viewable_subjects[$id]);
                $unit->userAccesses()->save($user, ['permission' => $permission]);
            }
            foreach ($viewable_subjects as $key => $value) {
                $unit = Subject::findOrFail($key);
                $unit->userAccesses()->save($user, ['permission' => PermissionPivot::READ]);
            }
        }
    }

    /**
     * Set the roles of the user
     *
     * Only users with edit-user ability can use this API
     *
     * You are free to use role titles or their respective ids in the array, but note that existing roles would be
     * removed if not included.
     *
     * @Put("/{user}/roles")
     * @Parameters({
     *     @Parameter("user", required=true, type="int"),
     * })
     * @Request(
     *     {
     *          "roles" : {
     *              "admin",
     *              "user",
     *              "role-a",
     *          }
     *      }
     * )
     *
     * @param Request $request
     * @param User $user
     */
    public function setRoles(Request $request, User $user)
    {
        $this->authorize('updatePermissions', $user);
        $data = $request->json()->all();
        if (isset($data['roles'])) {
            // re-sync user roles
            foreach ($data['roles'] as $roleId) {
                $role = Role::find($roleId);
                if ($role->name == 'super_super_admin') {
                    $this->authorize('create_system_admin');
                }
                if ($role->name == 'super_admin') {
                    $this->authorize('create_super_admin');
                }
            }
            $this->bouncer->sync($user)->roles($data['roles']);
        }
    }

    /**
     * Set the abilities of the user
     *
     * Only users with edit-user ability can use this API
     *
     * You are free to use role titles or their respective ids in the array, but note that existing roles would be
     * removed if not included.
     *
     * @Put("/{user}/abilities")
     * @Parameters({
     *     @Parameter("user", required=true, type="int"),
     * })
     * @Request(
     *     {
     *          "abilities" : {
     *              1, 2, 3, "edit-user-ability"
     *          }
     *      }
     * )
     *
     * @param Request $request
     * @param User $user
     */
    public function setAbilities(Request $request, User $user)
    {
        $this->authorize('updatePermissions', $user);
        $data = $request->json()->all();
        if (isset($data['abilities'])) {
            // re-sync user roles
            $this->bouncer->sync($user)->abilities($data['abilities']);
        }
    }

    /**
     * Get the abilities of the user
     *
     * @Get("/{user}/abilities")
     * @Parameters({
     *     @Parameter("user", required=true, type="int"),
     * })
     *
     * @param Request $request
     * @param User $user
     */
    public function getAbilities(Request $request, User $user)
    {
        $this->authorize('updatePermissions', $user);
        return $user->abilities();
    }

    /**
     * Get the users who have * or create_subject abilities
     *
     * @Get("/subject_abilities")
     *
     * @param Request $request
     * @param User $user
     */
    public function getSubjectAbilities(Request $request)
    {
        return User::whereHas('roles', function ($query) {
            $query->whereHas('abilities', function ($query) {
                $query->where('abilities.name', "create_subject")
                    ->orWhere(
                        function ($query) {
                            $query->where('abilities.entity_type', "*")->where('abilities.name', "*");
                        }
                    );
            });
        })->select('id', 'external_id', 'name')->get()->makeHidden(['cover_url', 'email_token_expired', 'viewable_root_departments', 'viewable_root_department', 'viewable_departments', 'department']);
    }

    /**
     * Set the extra departments managed by the user
     *
     * Only users with edit-user ability can use this API
     *
     * You are free to use role titles or their respective ids in the array, but note that existing roles would be
     * removed if not included.
     *
     * @Put("/{user}/viewable_departments")
     * @Parameters({
     *     @Parameter("user", required=true, type="int"),
     * })
     * @Request(
     *     {
     *          "departments" : {
     *              10003, 200000, 99999999
     *          }
     *      }
     * )
     *
     * @param Request $request
     * @param User $user
     */
    public function setDepartments(Request $request, User $user)
    {
        $this->authorize('update', $user);
        $data = $request->json()->all();
        if (isset($data['departments'])) {
            $departments = Department::findMany($data['departments'])->pluck((new Department)->getKeyName());
            $result = $user->viewableDepartments()->sync($departments);
            foreach ($result['detached'] as $detached) {
                $this->removeSubjectAbilitiesByUser($user, $detached, Ability::VIEW_SUBJECT);
            }
            foreach ($result['attached'] as $attached) {
                $this->addSubjectAbilitiesByUser($user, $attached, Ability::VIEW_SUBJECT);
            }
        }
    }

    public function badges(User $user, Request $request)
    {
        return $user->badges;
    }

    /**
     * Check user status (expired?)
     *
     * Number of users should be within 1000
     *
     * @Post("/check")
     * @Parameters({
     *     @Parameter("user_ids", required=true, type="array"),
     * })
     * @Request(
     *     {
     *          "users" : {"10000106", "33560050", "foobar"}
     *     }
     * )
     * @Response(200, body={
     *     "expired": {"10000106"},
     *     "locked": {"33560050"},
     *     "nonexistent": {"foobar"},
     * })
     *
     * @param Request $request
     * @return array
     * @throws BadRequestHttpException
     */
    public function checkStatus(Request $request)
    {
        $user_ids = $request->json('user_ids') ?? [];
        $external_ids = $request->json('external_ids') ?? [];
        $users = $this->userStub->whereIn('id', $user_ids)->orWhereIn('external_id', $external_ids)->get();
        $expired = [];
        $locked = [];
        $external_ids = array_reduce(
            $external_ids,
            static function (array $carry, string $item): array {
                return $carry + [$item => null];
            },
            []
        );
        $users->each(
            function (User $user) use (&$user_ids, &$expired, &$locked, &$external_ids) {
                $index = array_search($user->id, $user_ids, true);
                if ($index !== false) {
                    unset($user_ids[$index]);
                }
                if ($user->expiry !== null && (new Carbon)->gt($user->expiry)) {
                    $expired[] = $user->id;
                }
                if ($user->locked) {
                    $locked[] = $user->id;
                }
                $external_ids[$user->external_id] = $user->id;
            }
        );
        return [
            'expired' => $expired,
            'locked' => $locked,
            'nonexistent' => array_values($user_ids),
            'external_ids' => $external_ids
        ];
    }

    /**
     * allow super-admin to approve user registration
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function approveRegistration(Request $request)
    {
        $request->validate(["users" => "present"]);

        $this->authorize('manage_registration');

        $adminUser = $this->user();
        $user_ids = $request->input("users");
        $unit = $this->user()->unitModel;
        $curMemberCount = $unit->memberCount();

        // check enough seats
        if (!Unit::isDefault($unit) && $unit->memberFulled()) {
            throw new RegisterException("unit is fulled");
        }
        // get unapproved user(s) within the same unit only
        $users = User::whereIn("id", $user_ids)->whereNull("approved_at")->where("unit_id", $adminUser->unit_id)->get();

        // check if unit have enough seats
        if (!Unit::isDefault($unit) && $unit->seat_size < $users->count() + $curMemberCount) {
            throw new RegisterException("have not enough seats for all users");
        }

        // update approve time
        foreach ($users as $user) {
            $user->approved_at = Carbon::now();
            $user->save();
        }

        return new JsonResponse([
            "message" => "User(s) approved"
        ]);
    }
    /**
     * allow super-admin to reject user registration
     *
     * @param Request $request
     * @return array
     */
    public function rejectRegistration(Request $request)
    {
        $this->authorize('manage_registration');

        $adminUser = $this->user();
        $user_ids = $request->input("users");
        // get unapproved user(s) within the same unit only
        $users = User::whereIn("id", $user_ids)->whereNull("approved_at")->where("unit_id", $adminUser->unit_id);
        $deleted = $users->get();

        // start transaction
        DB::beginTransaction();

        // create record to rejected users table
        try {
            foreach ($deleted as $user) {
                $ruser = RejectedUser::create(["user_id" => $user->id] + $user->toArray());
            }

            $users->delete();
        } catch (Exception $e) {
            DB::rollback();
        }
        DB::commit();

        // return deleted users
        return $deleted;
    }

    public function setTemplate(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $user = $user->id;
        $content = $request->getContent();

        //make new directory if not exist
        if (!$this->getStorage()->exists(self::TEMPLATE_PATH)) {
            $this->getStorage()->makeDirectory(self::TEMPLATE_PATH);
        }
        //delete existing template
        if (!$this->getStorage()->exists(self::TEMPLATE_PATH . "$user.json")) {
            $this->getStorage()->delete(self::TEMPLATE_PATH . "$user.json");
        }
        //save the file
        $this->getStorage()->put(self::TEMPLATE_PATH . "$user.json", $content);

        return new EmptyResponse();
    }

    public function getTemplate(Request $request, User $user)
    {
        $this->authorize('update', $user);

        $user = $user->id;
        $path = self::TEMPLATE_PATH . "$user.json";
        //No template avaliable
        if (!$this->getStorage()->exists($path)) {
            throw new NotFoundHttpException(); //404
        } else {
            $json = json_decode($this->getStorage()->get($path), true);
            return $json;
        }
    }

    private function getStorage(): Cloud
    {
        if ($this->storage === null) {
            $this->storage = app(Cloud::class);
        }
        return $this->storage;
    }

    /**
     * @param Request $request
     * @param string $field
     * @return Paginator|\Illuminate\Support\Collection
     */
    private function getFieldItems(Request $request, string $field)
    {
        $query = $this->userStub->distinct()->whereNotNull($field)->orderBy($field);
        if ($request->query($field)) {
            $query->where($field, 'like', '%' . $request->query($field) . '%');
        }

        return $this->paginateCollection(
            $request,
            $query->pluck($field)
        );
    }

    public function getEditLog(User $user)
    {
        return $this->connection->table('access_records')
            ->leftJoin('users', 'user_id', '=', 'users.id')
            ->where('route_name', 'like', 'users.%')
            ->where('object_class', '=', User::class)
            ->where('object_id', '=', $user->id)
            ->orderBy('time', 'desc')
            ->select(['route_name', 'users.name', 'user_id', 'time', 'app_id', 'object_id', 'details'])
            ->paginateOrGet();
    }

    /**
     * List the downlin agents of the upline_code
     *
     * @Get("/{id}")
     * @Parameters({
     *     @Parameter("id") : the id of the upline agents
     * })
     *
     * @param Request $request
     * @return mixed
     */
    public function showDowline(Request $request, User $user)
    {

        ($this->userStub)::serializing(
            function (User $user) {
                $user->setHidden(array_diff($user->getHidden(), ['nid', 'email']));
            }
        );
        if ($request->query('unit_id') !== null) {
            $this->authorize('create_super_admin');
            UnitScope::$unit = Unit::findOrFail($request->query('unit_id'));
        }

        $query = $this->userStub->OfManager($user)->with(
            ['department', 'viewableRootDepartment', 'managing', 'abilities']
        )
            ->leftJoin('users as supervisors', 'users.supervisor_id', '=', 'supervisors.id')
            ->select(
                [
                    'users.*',
                    'supervisors.external_id as supervisor_external_id',
                    'supervisors.name as supervisor_name',
                    'supervisors.email as supervisor_email',
                ]
            );

        if ($query->getModel()->hasGlobalScope(ViewScope::class)) {
            /** @var ViewScope $scope */
            $scope = $query->getModel()->getGlobalScope(ViewScope::class);
            $query->addSelect($scope->getAlias($query->getModel()) . '.*');
        }
        $filter_data = Arr::except(
            $request->query(),
            [
                'password',
                'remember_token',
                'type',
                'secret',
                'name',
                'page',
                'per_page',
                'department_id',
                'department_ids',
                'has_rights',
                'has_no_rights',
                'roles',
                'abilities',
                'created_at_from',
                'created_at_to',
                'entry_date_from',
                'entry_date_to',
                'nid',
                'email',
                'first_name',
                'last_name',
                'full_name',
                'external_id',
            ]
        );
        foreach (
            [
                'supervisor_external_id' => 'supervisors.external_id',
                'supervisor_name' => 'supervisors.name',
                'supervisor_email' => 'supervisors.email',
            ] as $apparent => $real
        ) {
            if (array_key_exists($apparent, $filter_data)) {
                $filter_data[$real] = $filter_data[$apparent];
                unset($filter_data[$apparent]);
            }
        }
        $this->filterRequestArguments(
            $query,
            $filter_data
        );
        nullable(
            $request->query('department_id'),
            function ($department_id) use ($query, $request) {
                $department = Department::findOrFail($request->query('department_id'));
                $query->whereIn(
                    'users.department_id',
                    function (QueryBuilder $q) use ($department) {
                        $q->select('id')->from('departments')->whereBetween('_lft', [$department->_lft, $department->_rgt]);
                    }
                );
            }
        );
        nullable(
            $request->query('department_ids'),
            function (string $department_ids_json) use ($query) {
                $query->whereIn(
                    'users.department_id',
                    function (QueryBuilder $q) use ($department_ids_json) {
                        $q->select('id')->from('departments');
                        foreach (json_decode($department_ids_json, true) as $department_id) {
                            $department = Department::findOrFail($department_id);
                            $q->orWhereBetween('_lft', [$department->_lft, $department->_rgt]);
                        }
                    }
                );
            }
        );
        foreach (
            [
                'users.created_at' => ['created_at_from', 'created_at_to'],
                'users.entry_date' => ['entry_date_from', 'entry_date_to']
            ] as $field => [$from_field, $to_field]
        ) {
            nullable(
                $request->query($from_field),
                static function (string $from_value) use ($field, $query) {
                    $query->where($field, '>=', $from_value);
                }
            );
            nullable(
                $request->query($to_field),
                static function (string $to_value) use ($field, $query) {
                    $query->where($field, '<=', $to_value);
                }
            );
        }
        foreach (['name', 'email', 'nid', 'first_name', 'last_name', 'full_name', 'external_id'] as $field) {
            $input = $request->query($field);
            if ($input) {
                $wrapped_input = Arr::wrap($input);
                $query->where(
                    function (Builder $builder) use ($wrapped_input, $field) {
                        foreach ($wrapped_input as $item) {
                            $builder->orWhere('users.' . $field, 'like', '%' . $item . '%');
                        }
                    }
                );
            }
        }
        if ($request->query('has_rights')) {
            $query->where(
                function (Builder $query) {
                    $query->whereIn(
                        'users.id',
                        function (QueryBuilder $query) {
                            $query->from('assigned_roles')->select('entity_id')->where('entity_type', User::class);
                        }
                    );
                }
            );
        }
        if ($request->query('has_no_rights')) {
            $query->where(
                function (Builder $query) {
                    $query->whereNotIn(
                        'users.id',
                        function (QueryBuilder $query) {
                            $query->from('assigned_roles')->select('entity_id')->where('entity_type', User::class);
                        }
                    );
                }
            );
        }
        if ($request->query('page') !== null) {
            return $query->paginate();
        }

        $collection = new Collection();
        $query->chunk(
            1000,
            function (Collection $chunk) use (&$collection) {
                $collection = $collection->merge($chunk);
            }
        );
        $collection->each(
            function (User $user) {
                $user->setHidden(array_diff($user->getHidden(), ['nid']));
            }
        );
        return $collection;
    }

    /**
     * set email verified
     *
     * @param Request $request
     * @return EmptyResponse
     */
    public function setEmailVerified(Request $request, User $user)
    {
        $data = $request->validate([
            'email_verified' => 'boolean|required',
        ]);
        if ($data['email_verified']) {
            $user->email_verified_at = Carbon::now();
            $user->email_token = null;
            $user->save();
        } else {
            $user->email_verified_at = null;
            $user->email_token = null;
            $user->save();
        }
        return new EmptyResponse();
    }

    public function addSubjectAbilitiesByUser(User $new_user, ?string $department_id, ?string $ability)
    {
        if ($department_id === null) {
            // the user does not belong to any departments
            return false;
        }
        $department = Department::findOrFail($department_id);

        if ($new_user->can(Ability::CREATE_SUBJECT)) {
            // this user already has all course management ability
            return false;
        }

        foreach ($department->descendantsAndSelf as $dept) {
            $update = [];

            foreach ($dept->subjects as $subject) {
                $existing = app(SubjectController::class)->getSubjectAbilities($subject, new Request());
                if ($existing->contains('name', Ability::VIEW_SUBJECT)) {
                    foreach ($existing->where('name', Ability::VIEW_SUBJECT)->first()->users as $user) {
                        $update[$user->id] = [['name' => Ability::VIEW_SUBJECT, 'entity_type' => Subject::class, 'entity_id' => $subject->id]];
                    }
                }
                if ($existing->contains('name', Ability::EDIT_SUBJECT)) {
                    foreach ($existing->where('name', Ability::EDIT_SUBJECT)->first()->users as $user) {
                        $update[$user->id] = [['name' => Ability::EDIT_SUBJECT, 'entity_type' => Subject::class, 'entity_id' => $subject->id]];
                    }
                }
                if (!array_key_exists($new_user->id, $update)) {
                    $update[$new_user->id] = [['name' => $ability ?? Ability::VIEW_SUBJECT, 'entity_type' => Subject::class, 'entity_id' => $subject->id]];
                }
                app(SubjectController::class)->assignUsersAbility($subject, new Request($update));
            }
        }

        return new EmptyResponse();
    }

    public function removeSubjectAbilitiesByUser(?User $new_user, ?string $department_id, ?string $ability)
    {
        if ($new_user === null) {
            // to handle department does not have old managaer case
            return false;
        }
        if ($department_id === null) {
            // the user does not belong to any departments before
            return false;
        }
        $department = Department::findOrFail($department_id);

        foreach ($department->descendantsAndSelf as $dept) {
            foreach ($dept->subjects as $subject) {
                $existing = app(SubjectController::class)->getSubjectAbilities($subject, new Request());

                $update = [];

                if ($existing->contains('name', Ability::VIEW_SUBJECT)) {
                    foreach ($existing->where('name', Ability::VIEW_SUBJECT)->first()->users as $user) {
                        $update[$user->id] = [['name' => Ability::VIEW_SUBJECT, 'entity_type' => Subject::class, 'entity_id' => $subject->id]];
                    }
                }
                if ($existing->contains('name', Ability::EDIT_SUBJECT)) {
                    foreach ($existing->where('name', Ability::EDIT_SUBJECT)->first()->users as $user) {
                        $update[$user->id] = [['name' => Ability::EDIT_SUBJECT, 'entity_type' => Subject::class, 'entity_id' => $subject->id]];
                    }
                }
                if (array_key_exists($new_user->id, $update)) {
                    if (head($update[$new_user->id])['name'] == ($ability ?? Ability::VIEW_SUBJECT)) {
                        unset($update[$new_user->id]);
                    }
                }
                app(SubjectController::class)->assignUsersAbility($subject, new Request($update));
            }
        }
        return new EmptyResponse();
    }
}
