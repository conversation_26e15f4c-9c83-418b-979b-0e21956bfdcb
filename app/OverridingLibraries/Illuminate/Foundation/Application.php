<?php
declare(strict_types = 1);

namespace App\OverridingLibraries\Illuminate\Foundation;

use App\OverridingLibraries\Illuminate\Routing\RoutingServiceProvider;
use Illuminate\Events\EventServiceProvider;
use Illuminate\Foundation\Application as Original;
use Illuminate\Log\Context\ContextServiceProvider;
use Illuminate\Log\LogServiceProvider;

class Application extends Original
{

    protected function registerBaseServiceProviders()
    {
        $this->register(new EventServiceProvider($this));

        $this->register(new LogServiceProvider($this));

        $this->register(new ContextServiceProvider($this));

        $this->register(new RoutingServiceProvider($this));
    }
}
