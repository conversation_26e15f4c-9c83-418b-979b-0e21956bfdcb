<?php
declare(strict_types = 1);

namespace App\OverridingLibraries\Illuminate\Foundation\Http;

use App\Http\Middleware\AddSecurityHeaders;
use App\Http\Middleware\CheckClientId;
use App\Http\Middleware\DatabaseTransaction;
use App\Http\Middleware\DecryptRequest;
use App\Http\Middleware\EncryptResponse;
use App\Http\Middleware\Json;
use App\Http\Middleware\LogRequest;
use App\Http\Middleware\LogResponse;
use App\Http\Middleware\RecordDatabaseQuery;
use App\Http\Middleware\SetLanguage;
use App\Http\Middleware\SetRequestFormat;
use App\Http\Middleware\SetUser;
use App\Http\Middleware\Check2fa;
use App\Http\Middleware\TrustProxies;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\Authorize;
use Illuminate\Foundation\Http\Kernel as Original;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class Kernel extends Original
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        TrustProxies::class,
        Json::class,
        CheckForMaintenanceMode::class,
        SetRequestFormat::class,
        SetLanguage::class,
        AddSecurityHeaders::class,
        LogRequest::class,
        LogResponse::class,
        HandleCors::class,
        SetRequestFormat::class,
        RecordDatabaseQuery::class,
        DatabaseTransaction::class,
        SetUser::class,
    ];

    /**
     * The priority-sorted list of middleware.
     *
     * Forces the listed middleware to always be in the given order.
     *
     * @var array
     */
    protected $middlewarePriority = [
        StartSession::class,
        ShareErrorsFromSession::class,
        CheckClientId::class,
        Authenticate::class,
        AuthenticateSession::class,
        SubstituteBindings::class,
        Authorize::class,
    ];

    protected $routeMiddleware = [
        'can' => Authorize::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'api' => [
            //ThrottleRequests::class . ':60,1',
            SubstituteBindings::class,
        ],
        'web' => [
            SubstituteBindings::class,
        ],
        'authed' => [
            CheckClientId::class,
            EncryptResponse::class,
            DecryptRequest::class,
            Authenticate::class,
        ]
    ];
}
