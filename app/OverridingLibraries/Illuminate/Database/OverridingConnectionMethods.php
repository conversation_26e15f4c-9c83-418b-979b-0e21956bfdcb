<?php
declare(strict_types=1);

namespace App\OverridingLibraries\Illuminate\Database;

use Closure;
use Exception;

trait OverridingConnectionMethods {
    /**
     * Run a SQL statement.
     *
     * @param  string  $query
     * @param  array  $bindings
     * @param  \Closure  $callback
     * @return mixed
     *
     * @throws \Illuminate\Database\QueryException
     */
    protected function runQueryCallback($query, $bindings, Closure $callback)
    {
        // To execute the statement, we'll simply call the callback, which will actually
        // run the SQL against the PDO connection. Then we can calculate the time it
        // took to execute and log the query SQL, bindings and time in our memory.
        try {
            $result = $callback($query, $bindings);
        }

            // If an exception occurs when attempting to run a query, we'll format the error
            // message to include the bindings with SQL, which will make this exception a
            // lot more helpful to the developer instead of just the database's errors.
        catch (Exception $e) {
            /** @noinspection PhpParamsInspection */
            throw new QueryException(
                $this->getName(), $query, $this->prepareBindings($bindings), $e, $this
            );
        }

        return $result;
    }

    abstract public function prepareBindings(array $bindings);

}
