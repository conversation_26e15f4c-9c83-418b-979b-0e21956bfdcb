<?php
declare(strict_types=1);

namespace App\OverridingLibraries\Illuminate\Database;

use Illuminate\Database\Connection;
use Illuminate\Database\QueryException as Original;
use Illuminate\Support\Str;
use Throwable;
use function is_string;

class QueryException extends Original {
    /**
     * @var Connection
     */
    protected $connection;

    public function __construct($connectionName, $sql, array $bindings, Throwable $previous, Connection $connection = null) {
        $this->connection = $connection;
        parent::__construct($connectionName, $sql, $bindings, $previous);
    }

    /**
     * Format the SQL error message.
     *
     * @param  string  $connectionName
     * @param  string  $sql
     * @param  array  $bindings
     * @param  \Throwable  $previous
     * @return string
     */
    protected function formatMessage($connectionName, $sql, $bindings, Throwable $previous)
    {
        if ($this->connection) {
            $bindings = array_map(
                function ($binding) {
                    return is_string($binding) ? $this->connection->getQueryGrammar()->quoteString($binding) : $binding;
                },
                $bindings
            );
        }
        return $previous->getMessage().' (Connection: '.$connectionName.', SQL: '.Str::replaceArray('?', $bindings, $sql).')';
    }
}
