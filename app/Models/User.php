<?php

/** @noinspection PhpUndefinedClassInspection */
/** @noinspection PhpComposerExtensionStubsInspection */

declare(strict_types=1);

namespace App\Models;

use App\Mail\VerifySyncEmail;
use App\CtfUpdate\UserMapper;
use App\Encryption\KeyManager;
use App\Mail\VerifySchoolTeacherEmail;
use App\Models\Learning\Assignment;
use App\Models\Learning\Attachment;
use App\Models\Learning\Badge;
use App\Models\Learning\CmsCourse;
use App\Models\Learning\Comment;
use App\Models\Learning\Course;
use App\Models\Learning\CourseMaster;
use App\Models\Learning\CourseRecommendation;
use App\Models\Learning\DiscussionTopic;
use App\Models\Learning\DiscussionTopicReply;
use App\Models\Learning\HasAcl;
use App\Models\Learning\Item;
use App\Models\Learning\ItemResponse;
use App\Models\Learning\LearningMap;
use App\Models\Learning\Lesson;
use App\Models\Learning\Like;
use App\Models\Learning\Manager;
use App\Models\Learning\MockExamUserAttempt;
use App\Models\Learning\NewBelongsToManyWithEnrollment;
use App\Models\Learning\Quiz;
use App\Models\Learning\Subject;
use App\Models\Learning\Theme;
use App\Models\Learning\Vocab;
use App\Models\Pivots\AttachmentUser;
use App\Models\Pivots\BadgeUser;
use App\Models\Pivots\CourseHelper;
use App\Models\Pivots\CourseUser;
use App\Models\Pivots\CourseUserAcl;
use App\Models\Pivots\ItemUser;
use App\Models\Pivots\LearningMapUser;
use App\Models\Pivots\LessonUser;
use App\Models\Pivots\QuizUser;
use App\Models\Pivots\SubjectUserAcl;
use App\Models\Pivots\ThemeUserAcl;
use App\Models\Pivots\UserUserNotification;
use App\Models\Pivots\UserVocab;
use App\Models\Role;
use App\Models\School\School;
use App\Models\School\SchoolEventRegistration;
use App\Models\School\SchoolEventRegistrationMember;
use App\Models\School\SchoolRegistration;
use App\Models\School\SchoolRegistrationMember;
use App\Models\UserZoom;
use App\Notifications\ManagerPendingNotification;
use App\Notifications\PreResetPassword;
use App\Notifications\PurchaseUnitProductSucceededNotification;
use App\Notifications\PurchaseUserProductSucceededNotification;
use App\Notifications\Push\ApnPusher;
use App\Notifications\Push\BaiduPusher;
use App\Notifications\ResetPassword;
use App\Notifications\SubscriptionRenewalFailedNotification;
use App\Notifications\SubscriptionRenewedNotification;
use App\Notifications\SubscriptionSucceededNotification;
use App\Notifications\SubscriptionUpgradedNotification;
use App\Notifications\VerifyEmailAdress;
use App\OverridingLibraries\Illuminate\Database\Eloquent\Model;
use App\Scopes\UnderDepartmentsScope;
use App\Scopes\UnderDepartmentsOrViewableScope;
use App\Scopes\UnitScope;
use App\Scopes\ViewScope;
use Auth;
use Carbon\Carbon;
use Eloquent;
use finfo;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Contracts\Filesystem\Cloud;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Support\Str;
use Imagick;
use JsonException;
use Kalnoy\Nestedset\Collection as NestedSetCollection;
use Laravel\Cashier\Billable;
use App\OverridingLibraries\Kalnoy\Nestedset\NodeTrait;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Facades\Cache;
use Mdanter\Ecc\Crypto\Key\PublicKeyInterface;
use ReflectionClass;

// use Silber\Bouncer\Database\HasRolesAndAbilities;
use Stripe\Customer;
use Stripe\StripeClient;
use Symfony\Component\HttpKernel\Exception\UnsupportedMediaTypeHttpException;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * App\Models\User
 *
 * @property string $id
 * @property string $name
 * @property string|null $unit used by rookie only
 * @property string|null $department_id
 * @property string|null $title
 * @property string|null $password
 * @property string|null $nid
 * @property string|null $phone
 * @property string|null $supervisor_id
 * @property \Illuminate\Support\Carbon|null $entry_date
 * @property string|null $gender
 * @property string|null $education_level used by pru-iiqe only
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $email
 * @property bool $locked
 * @property bool $pending_to_delete
 * @property \Illuminate\Support\Carbon|null $delete_date
 * @property string|null $address used by pru-iiqe only
 * @property string|null $viewable_root_department_id
 * @property int $privileges
 * @property \Illuminate\Support\Carbon|null $expiry
 * @property string|null $icon
 * @property string|null $cover
 * @property string|null $role
 * @property int|null $user_role_id
 * @property string $external_id
 * @property string|null $access_token
 * @property string|null $refresh_token
 * @property int|null $oauth_expires
 * @property string|null $name_en
 * @property string|null $name_zh
 * @property int $unit_id
 * @property string|null $division used by rookie only
 * @property int $login_attempts_count
 * @property int $course_point
 * @property \Illuminate\Support\Carbon|null $last_login_time
 * @property \Illuminate\Support\Carbon|null $latest_login_time
 * @property \Illuminate\Support\Carbon|null $password_changed_at
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $document_type used by pru-iiqe only
 * @property string|null $birthday
 * @property string|null $language
 * @property string|null $full_name
 * @property int|null $school_id
 * @property int $event_point
 * @property int $event_point_accumulate
 * @property-read Collection|Ability[] $abilities
 * @property-read int|null $abilities_count
 * @property-read Collection|Course[] $assignedCourses
 * @property-read int|null $assigned_courses_count
 * @property-read Collection|Attachment[] $attachments
 * @property-read int|null $attachments_count
 * @property-read Collection|Badge[] $badges
 * @property-read int|null $badges_count
 * @property-read Collection|Channel[] $channels
 * @property-read int|null $channels_count
 * @property-read Collection|CmsCourse[] $coursePermissions
 * @property-read int|null $course_permissions_count
 * @property-read Collection|CourseRecommendation[] $courseRecommendationsAsRecommendee
 * @property-read int|null $course_recommendations_as_recommendee_count
 * @property-read Collection|CourseRecommendation[] $courseRecommendationsAsRecommender
 * @property-read int|null $course_recommendations_as_recommender_count
 * @property-read Collection|Course[] $courses
 * @property-read int|null $courses_count
 * @property-read Department|null $department
 * @property-read Collection|Device[] $devices
 * @property-read int|null $devices_count
 * @property-read Collection|DiscussionTopicReply[] $discussionTopicReplies
 * @property-read int|null $discussion_topic_replies_count
 * @property-read Collection|DiscussionTopic[] $discussionTopics
 * @property-read int|null $discussion_topics_count
 * @property-read Collection|Course[] $course_recommended
 * @property-read string|null $cover_url
 * @property-read string|null $icon_url
 * @property-read array $privilege_strings
 * @property-read mixed $viewable_root_departments
 * @property-read mixed $work_email
 * @property-read Collection|UserHistory[] $histories
 * @property-read int|null $histories_count
 * @property-read Collection|ItemResponse[] $itemResponses
 * @property-read int|null $item_responses_count
 * @property-read Collection|Item[] $items
 * @property-read int|null $items_count
 * @property-read Collection|LearningMap[] $learningMaps
 * @property-read int|null $learning_maps_count
 * @property-read Collection|Lesson[] $lessons
 * @property-read int|null $lessons_count
 * @property-read Collection|Like[] $likesRelation
 * @property-read int|null $likes_relation_count
 * @property-read NestedSetCollection|Department[] $managing
 * @property-read int|null $managing_count
 * @property-read Collection|MockExamUserAttempt[] $mock_exam_attempts
 * @property-read int|null $mock_exam_attempts_count
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection|PasswordResetToken[] $passwordResetTokens
 * @property-read int|null $password_reset_tokens_count
 * @property-read Collection|Quiz[] $quizzes
 * @property-read int|null $quizzes_count
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $readNotifications
 * @property-read int|null $read_notifications_count
 * @property-read Collection|Role[] $roles
 * @property-read int|null $roles_count
 * @property-read Collection|Subject[] $subjectPermissions
 * @property-read int|null $subject_permissions_count
 * @property-read Collection|User[] $subordinates
 * @property-read int|null $subordinates_count
 * @property-read User|null $supervisor
 * @property-read Collection|Course[] $teachedCourses
 * @property-read int|null $teached_courses_count
 * @property-read Collection|Theme[] $themePermissions
 * @property-read int|null $theme_permissions_count
 * @property-read Unit $unitModel
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $unreadNotifications
 * @property-read int|null $unread_notifications_count
 * @property-read Collection|UserNotification[] $userNotifications
 * @property-read int|null $user_notifications_count
 * @property-read NestedSetCollection|Department[] $viewableDepartments
 * @property-read int|null $viewable_departments_count
 * @property-read Department|null $viewableRootDepartment
 * @property-read Collection|Vocab[] $vocabs
 * @property-read int|null $vocabs_count
 * @property-read School|null $school
 * @property-read Collection|SchoolRegistration $currentApprovedSchoolRegistration
 * @property-read Collection|SchoolRegistrationMember $currentApprovedSchoolRegistrationMember
 * @property-read bool $is_teacher
 * @property-read bool $is_principal
 * @method static Builder|User inViewableRootDepartments($key = 'users.department_id')
 * @method Builder|User newModelQuery()
 * @method Builder|User newQuery()
 * @method static Builder|User onlyCurrent()
 * @method static Builder|User onlyUser(User $user)
 * @method static Builder|User query()
 * @method static Builder|User underDepartments($departments)
 * @method static Builder|User whereAccessToken($value)
 * @method static Builder|User whereAddress($value)
 * @method static Builder|User whereBirthday($value)
 * @method static Builder|User whereCover($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDepartmentId($value)
 * @method static Builder|User whereDivision($value)
 * @method static Builder|User whereDocumentType($value)
 * @method static Builder|User whereEducationLevel($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEntryDate($value)
 * @method static Builder|User whereExpiry($value)
 * @method static Builder|User whereExternalId($value)
 * @method static Builder|User whereFirstName($value)
 * @method static Builder|User whereFullName($value)
 * @method static Builder|User whereGender($value)
 * @method static Builder|User whereIcon($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereIs($role)
 * @method static Builder|User whereIsAll($role)
 * @method static Builder|User whereIsNot($role)
 * @method static Builder|User whereLanguage($value)
 * @method static Builder|User whereLastLoginTime($value)
 * @method static Builder|User whereLastName($value)
 * @method static Builder|User whereLatestLoginTime($value)
 * @method static Builder|User whereLocked($value)
 * @method static Builder|User wherePendingToDelete($value)
 * @method static Builder|User whereLoginAttemptsCount($value)
 * @method static Builder|User whereName($value)
 * @method static Builder|User whereNameEn($value)
 * @method static Builder|User whereNameZh($value)
 * @method static Builder|User whereNid($value)
 * @method static Builder|User whereOauthExpires($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePasswordChangedAt($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePrivileges($value)
 * @method static Builder|User whereRefreshToken($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereRequestArguments($arguments, $date_column_name = 'month')
 * @method static Builder|User whereRole($value)
 * @method static Builder|User whereSupervisorId($value)
 * @method static Builder|User whereTitle($value)
 * @method static Builder|User whereUnit($value)
 * @method static Builder|User whereUnitId($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUserRoleId($value)
 * @method static Builder|User whereViewableRootDepartmentId($value)
 * @method static Builder|User withPermission(HasAcl $item, $permission)
 * @mixin Eloquent
 */
class User extends Model implements
    Notifiable,
    CtfUpdatable,
    AuthenticatableContract,
    CanResetPasswordContract,
    AuthorizableContract,
    JWTSubject,
    HasLocalePreference
{
    use Authenticatable;
    use Authorizable;
    use CanResetPassword;
    use CtfTraitWithoutDelete;

    use NotifiableTrait {
        routeNotificationFor as private traitRouteNotificationFor;
    }
    use HashPassword;
    use Billable;
    use NewBelongsToManyWithEnrollment;
    // use HasRolesAndAbilities;
    // Custom implementation to replace Bouncer's HasRolesAndAbilities trait

    use NodeTrait;

    public const ICONS_PATH = 'users/icons';
    public const COVERS_PATH = 'users/covers';
    public const CERTIFICATES_PATH = 'users/certificates';

    public const PRIVILEGE_MODERATE_DISCUSSIONS = 1;
    public const PRIVILEGE_MANAGE_NOTIFICATIONS = 2;
    public const PRIVILEGE_MANAGE_COURSE_MATERIALS = 4;
    public const PRIVILEGE_VIEW_OTHER_DEPARTMENTS = 8;
    public const PRIVILEGE_MANAGE_USERS = 16;
    public const PRIVILEGE_CHANGE_USERS_PRIVILEGES = 32;
    public const PRIVILEGE_MANAGE_REPORTS = 64;
    public const PRIVILEGE_TEACHER_SITE_ACCESS = 128;
    public const PRIVILEGE_CMS_SITE_ACCESS = 256;
    public const PRIVILEGE_GOD = (1 << 31) - 1;
    public const GRACE_PERIOD_SECONDS = 60 * 60 * 24;

    private const SUPPORTED_MIME_TYPES = [
        'image/png' => 'png',
        'image/jpeg' => 'jpg',
    ];

    private const IMAGE_MAPPINGS = [
        'icon' => self::ICONS_PATH,
        'cover' => self::COVERS_PATH,
    ];

    public $incrementing = false;

    protected $attributes = [
        'email_token' => null
    ];
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'nid',
        'document_type',
        'password',
        'remember_token',
        'managing',
        'phone',
        'email',
        'initial_password',
        'access_token',
        'refresh_token',
        'oauth_expires',
        'resetPasswordTokens',
        'email_token',
        'is_unit_admin',
        'is_default_unit',
        'is_system_admin',
        'unitModel',
        'user_lft',
        'user_rgt',
        'ac_num',
        'email_token_expired',
        'forbidden_abilities'
    ];
    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'icon_url',
        'cover_url',
        'email_token_expired',
        'forbidden_abilities',
        'is_unit_admin',
        'is_default_unit',
        'is_system_admin',
        'is_teacher',
        'is_principal',
        'unit_external_id',
        'viewable_root_departments'
    ];

    protected $casts = [
        'ratio_a' => 'float',
        'ratio_e' => 'float',
        'ratio_f' => 'float',
        'ratio_m' => 'float',
        'department_id' => 'string',
        'job_code' => 'int',
        'level' => 'int',
        'locked' => 'bool',
        'pending_to_delete' => 'bool',
        'use2fa' => 'bool',
    ];

    protected $dates = [
        'entry_date',
        'expiry',
        'last_login_time',
        'latest_login_time',
        'password_changed_at',
        'approved_at',
        'delete_date',
    ];

    protected $fillable = [
        'name',
        'email',
        'first_name',
        'last_name',
        'google2fa_secret',
        'status',
        'district',
        'user_lft',
        'user_rgt',
        'agent_title',
        'upline_code',
        'supervisor_id',
        'upline_name_zh',
        'upline_name_en',
        'name_zh',
        'entry_date',
        'prod_start_date',
        'termination_date',
        'locked_reason',
        'locked',
        'phone',
        'expiry',
        'department_id',
        'title'
    ];

    protected $with = [
        'school',
        'viewableRootDepartment',
        'managing',
    ];

    protected $ownerships = [
        'badges',
        'devices',
        'courses',
        'lessons',
        'items',
        'attachments',
        'assignedCourses',
        'courseRecommendationsAsRecommender',
        'courseRecommendationsAsRecommendee',
        'quizzes',
        'mock_exam_attempts',
        'histories',
        'viewableDepartments',
        'likesRelation',
        'discussionTopics',
        'discussionTopicReplies',
        'passwordResetTokens'
        //, 'accessRecords'
    ];

    protected $keyType = 'string';

    /** @var Cloud|null */
    private $storage;

    /** @var Hasher|null */
    private $hasher;

    /** @var array|null */
    private $ctfRow = null;

    public static function getPrivilegeString(int $privilege): string
    {
        return array_keys(
            array_filter(
                (new ReflectionClass(User::class))->getConstants(),
                function ($value, $key) use ($privilege) {
                    return $value === $privilege && Str::startsWith($key, 'PRIVILEGE_');
                },
                ARRAY_FILTER_USE_BOTH
            )
        )[0] ?? (string)$privilege;
    }

    public static function makeAsStripeCustomer(User $user): Customer
    {
        try {
            $stripe = new StripeClient(config('services.stripe.secret'));
            $customer = $stripe->customers->retrieve(
                $user->stripe_id,
                []
            );
            if (property_exists($customer, 'deleted')) throw new JsonException("deleted customer");

            $stripeCustomer = $user->createOrGetStripeCustomer();

            // Check the customers is unit admin or not
            $description = "Normal Customer";
            if ($user->is_unit_admin) {
                $description = "Unit Customer: " . $user->unitModel->external_id;
            }
            // Insert more details to stripe
            $stripe->customers->update(
                $user->stripe_id,
                [
                    'name' => $user->name,
                    'description' => $description,

                ]
            );
            return $stripeCustomer;
        } catch (\Throwable $th) {
            $user->stripe_id = null;
            $user->save();
            $stripeCustomer = $user->createOrGetStripeCustomer();
            return $stripeCustomer;
        }
    }

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new UnitScope());
        static::creating(
            function (User $user) {
                if ($user->unit_id === null) {
                    $user->load('department');
                    $user->unit_id = nullable(
                        $user->department,
                        function (Department $department) {
                            return $department->unit_id;
                        }
                    ) ?? Unit::getDefault()->id;
                }
                if ($user->external_id === null) {
                    $user->external_id = $user->id;
                }
            }
        );
        static::saving(
            function (User $user) {
                if ($user->isDirty('password')) {
                    $user->password_changed_at = new Carbon;
                }
                if ($user->stripe_id) {
                    $user->updateStripeCustomer([
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'name' => $user->name,
                    ]);
                }
                if ($user->delete_date !== null) {
                    $user->pending_to_delete = true;
                }
            }
        );
        static::deleted(
            function (self $user) {
                foreach (self::IMAGE_MAPPINGS as $field => $prefix) {
                    nullable(
                        $user->getAttribute($field),
                        function (string $file_name) use ($user, $prefix) {
                            /** @var Cloud $storage */
                            $storage = $user->getStorage();
                            $storage->delete("$prefix/$file_name");
                        }
                    );
                }
            }
        );
    }

    public function routeNotificationForMail()
    {
        return $this->getEmailForPasswordReset();
    }

    public function getEmailForPasswordReset()
    {
        if ($this->email !== null && strlen($this->email) > 0) {
            return $this->email;
        }

        if (config('mail.test_address')) {
            return config('mail.test_address');
        }

        return null;
    }

    public function sendPasswordResetNotification($token)
    {
        (new ResetPassword($token))->toMail($this)->send(app(Mailer::class));
    }

    public function sendPrePasswordResetNotification($token)
    {
        (new PreResetPassword($token))->toMail($this)->send(app(Mailer::class));
    }

    public function sendManagerNotification($pending_number)
    {
        (new ManagerPendingNotification($pending_number))->toMail($this)->send(app(Mailer::class));
    }

    public function getEmailForSyncToAgents($token)
    {
        $token = $this->SyncVerify()->where('token', $token)->first();
        if ($token->sync_email !== null && strlen($token->sync_email) > 0) {
            return $token->sync_email;
        }

        if (config('mail.test_address')) {
            return config('mail.test_address');
        }

        return null;
    }

    public function sendSyncToagentsNotification($token)
    {
        Mail::to($this->getEmailForSyncToAgents($token))->send(new VerifySyncEmail($this, $token, app(Request::class)->header('X-Ctf-App-Id')));
    }

    public function sendVerifyEmailAdressNotification($token)
    {
        (new VerifyEmailAdress($token))->toMail($this)->send(app(Mailer::class));
    }

    public function sendVerifySchoolTeacherEmail($password, $is_principal = false)
    {
        Mail::to($this->email)->send(new VerifySchoolTeacherEmail($this, $password, $is_principal, app(Request::class)->header('X-Ctf-App-Id')));
    }

    public function sendSubscriptionSucceededNotification($subscription, $invoice_url)
    {
        (new SubscriptionSucceededNotification($subscription, $invoice_url))->toMail($this)->send(app(Mailer::class));
    }

    public function sendSubscriptionUpgradedNotification($subscription, $invoice_url)
    {
        (new SubscriptionUpgradedNotification($subscription, $invoice_url))->toMail($this)->send(app(Mailer::class));
    }

    public function sendSubscriptionRenewedNotification($subscription, $invoice_url)
    {
        (new SubscriptionRenewedNotification($subscription, $invoice_url))->toMail($this)->send(app(Mailer::class));
    }

    public function sendSubscriptionRenewalFailedNotification($subscription, $invoice_url)
    {
        (new SubscriptionRenewalFailedNotification($subscription, $invoice_url))->toMail($this)->send(app(Mailer::class));
    }

    public function sendPurchaseUnitProductSucceededNotification($product, $receipt_url)
    {
        (new PurchaseUnitProductSucceededNotification($product, $receipt_url))->toMail($this)->send(app(Mailer::class));
    }

    public function sendPurchaseUserProductSucceededNotification($product, $receipt_url)
    {
        (new PurchaseUserProductSucceededNotification($product, $receipt_url))->toMail($this)->send(app(Mailer::class));
    }

    ///////////////////////
    //// Relationships ////
    ///////////////////////

    public function badges(): BelongsToMany
    {
        return $this->belongsToMany(Badge::class, BadgeUser::class);
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(Manager::class);
    }

    public function devices(): BelongsToMany
    {
        return $this->belongsToMany(Device::class)->withPivot(['app_id'])->withTimestamps();
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(self::class);
    }

    public function subordinates(): HasMany
    {
        return $this->hasMany(self::class, 'supervisor_id');
    }

    public function managing(): HasMany
    {
        return $this->hasMany(Department::class, 'manager_id');
    }

    /**
     * @return BelongsToMany|Course
     */
    public function courses(): BelongsToMany
    {
        return $this->belongsToMany(Course::class, CourseUser::class);
    }

    public function helpingCourses(): BelongsToMany
    {
        return $this->belongsToMany(Course::class, CourseHelper::class);
    }

    public function lessons(): BelongsToMany
    {
        return $this->belongsToMany(Lesson::class, LessonUser::class);
    }

    public function items(): BelongsToMany
    {
        return $this->belongsToMany(Item::class, ItemUser::class);
    }

    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, AttachmentUser::class);
    }

    public function assignedCourses(): MorphToMany
    {
        return $this->morphToMany(Course::class, 'assignment', Assignment::class);
    }

    public function courseRecommendationsAsRecommender(): HasMany
    {
        return $this->hasMany(CourseRecommendation::class, 'recommender_id');
    }

    public function courseRecommendationsAsRecommendee(): HasMany
    {
        return $this->hasMany(CourseRecommendation::class, 'recommendee_id');
    }

    public function quizzes(): BelongsToMany
    {
        return $this->belongsToMany(Quiz::class, QuizUser::class);
    }

    public function mock_exam_attempts(): HasMany
    {
        return $this->HasMany(MockExamUserAttempt::class, 'user_id');
    }

    public function histories(): HasMany
    {
        return $this->hasMany(UserHistory::class);
    }

    public function viewableRootDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function viewableDepartments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'department_manager');
    }

    public function likesRelation(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    public function discussionTopics(): HasMany
    {
        return $this->hasMany(DiscussionTopic::class);
    }

    public function discussionTopicReplies(): HasMany
    {
        return $this->hasMany(DiscussionTopicReply::class);
    }

    public function passwordResetTokens(): HasMany
    {
        return $this->hasMany(PasswordResetToken::class);
    }

    public function SyncVerify(): HasMany
    {
        return $this->hasMany(VerifyUser::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * @return Collection|Course[]
     */
    public function getCourseRecommendedAttribute(): Collection
    {
        return app(Course::class)->whereIn('course_master_id', $this->courseRecommendationsAsRecommendee()
            ->pluck('course_master_id'))
            ->onlyLatestVersion()
            ->onlyMyDepartment($this->department_id)
            ->get();
    }

    public function teachedCourses(): BelongsToMany
    {
        return $this->belongsToMany(Course::class, 'course_teacher');
    }

    public function learningMaps(): BelongsToMany
    {
        return $this->belongsToMany(LearningMap::class, LearningMapUser::class);
    }

    /**
     * @return BelongsToMany|Subject
     */
    public function subjectPermissions(): BelongsToMany
    {
        return $this->belongsToMany(Subject::class, SubjectUserAcl::class);
    }

    /**
     * @return BelongsToMany|Theme
     */
    public function themePermissions(): BelongsToMany
    {
        return $this->belongsToMany(Theme::class, ThemeUserAcl::class);
    }

    public function coursePermissions(): BelongsToMany
    {
        return $this->belongsToMany(CmsCourse::class, CourseUserAcl::class);
    }

    public function userNotifications(): BelongsToMany
    {
        return $this->belongsToMany(UserNotification::class, UserUserNotification::class);
    }

    public function channels(): HasMany
    {
        return $this->hasMany(Channel::class);
    }

    public function unitModel(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function itemResponses(): HasMany
    {
        return $this->hasMany(ItemResponse::class);
    }

    public function vocabs(): BelongsToMany
    {
        return $this->belongsToMany(Vocab::class, UserVocab::class);
    }

    public function zoomAccount(): HasOne
    {
        return $this->hasOne(UserZoom::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function getSchool(): School|null
    {
        return $this->school()->first();
    }

    public function schoolRegistrations(): HasManyThrough
    {
        return $this->hasManyThrough(
            SchoolRegistration::class,
            SchoolRegistrationMember::class,
        );
    }

    public function schoolRegistrationMembers(): HasMany
    {
        return $this->hasMany(SchoolRegistrationMember::class, 'user_id');
    }

    public function lastApprovedSchoolRegistration(): SchoolRegistration|null
    {
        return
            SchoolRegistration::whereHas(
                'schoolRegistrationMembers',
                function (Builder $query) {
                    $query->where('user_id', $this->id);
                }
            )
            ->where('status', 'approved')
            //取最新的
            ->orderBy('id', 'desc')
            //取第一筆
            ->first();
    }

    public function schoolEventRegistrationMembers(): HasManyThrough
    {
        return $this->hasManyThrough(
            SchoolEventRegistrationMember::class,
            SchoolRegistrationMember::class,
            'user_id',
            'school_registration_member_id',
        );
    }

    public function currentApprovedSchoolRegistration(): HasOneThrough
    {
        $year = Cache::get('school_registration_year');
        if ($year == null) {
            $year = '';
        }

        return $this->hasOneThrough(
            SchoolRegistration::class,
            SchoolRegistrationMember::class,
            'user_id',
            'id',
            'id',
            'school_registration_id'
        )
            ->where('year', $year)
            ->where('status', 'approved');
    }

    public function getCurrentApprovedSchoolRegistration(): SchoolRegistration|null
    {
        return $this->currentApprovedSchoolRegistration()->first();
    }

    public function currentApprovedSchoolRegistrationMember(): HasOne
    {
        $year = Cache::get('school_registration_year');
        if ($year == null) {
            $year = '';
        }
        return $this->hasOne(SchoolRegistrationMember::class, 'user_id')
            ->whereHas(
                'schoolRegistration',
                function (Builder $query) use ($year) {
                    $query->where('year', $year)
                        ->where('status', 'approved');
                }
            )->latestOfMany();
    }

    public function getCurrentApprovedSchoolRegistrationMember(): SchoolRegistrationMember|null
    {
        return $this->currentApprovedSchoolRegistrationMember()->first();
    }

    public function applicantEventRegistrations(): HasMany
    {
        return $this->hasMany(SchoolEventRegistration::class, 'applicant_id');
    }

    //////////////////
    //// Mutators ////
    //////////////////

    public function getPhoneAttribute($value)
    {
        return $value ?? $this->phone_with_default;
    }

    public function getID()
    {
        return $this->external_id;
    }


    public function getEmailAttribute($value)
    {
        return $value ?? $this->email_with_default;
    }

    public function setEmailTokenAttribute($value)
    {
        $this->attributes['email_token'] = nullable(
            $value,
            function (string $token) {
                return $token . '@' . Carbon::now()->addMinutes(config('mail.mail_token_expire'))->timestamp;
            }
        );
    }

    public function getEmailTokenAttribute($value)
    {
        return nullable(
            $value,
            function (string $token) {
                return explode('@', $token)[0];
            }
        );
    }

    public function getEmailTokenExpiredAttribute()
    {
        return nullable(
            $this->attributes['email_token'] ?? null,
            function (string $token) {
                return explode('@', $token)[1] < Carbon::now()->timestamp;
            }
        ) ?? true;
    }

    public function getIconUrlAttribute(): ?string
    {
        return nullable(
            $this->icon,
            function (string $path) {
                return url($this->getStorage()->url(self::IMAGE_MAPPINGS['icon'] . "/$path"));
            }
        );
    }

    public function getCoverUrlAttribute(): ?string
    {
        return nullable(
            $this->cover,
            function (string $path) {
                return url($this->getStorage()->url(self::IMAGE_MAPPINGS['cover'] . "/$path"));
            }
        );
    }

    public function getIsUnitAdminAttribute(): bool
    {
        // return $this->can('update', $this->unitModel) && !$this->is_default_unit;
        return $this->isA(Role::SUPER_ADMIN);
    }

    public function getIsDefaultUnitAttribute(): bool
    {
        if ($this->unitModel) {
            return Unit::isDefault($this->unitModel);
        } else {
            return true;
        }
    }

    public function getIsSystemAdminAttribute(): bool
    {
        return $this->isA(Role::SUPER_SUPER_ADMIN);
    }

    public function getUnitExternalIdAttribute(): ?string
    {
        return $this->unitModel->external_id ?? null;
    }

    public function isManager(): bool
    {
        if (!is_null($this->manager_id)) {
            return true;
        } else {
            return false;
        }
    }

    public function getIsTeacherAttribute(): bool
    {
        return $this->isA(Role::TEACHER);
    }

    public function getIsPrincipalAttribute(): bool
    {
        return $this->isA(Role::PRINCIPAL);
    }

    //////////////
    /// Scopes ///
    //////////////

    public function scopeOnlyAgentCurrent(Builder $query): Builder
    {
        return $query
            ->where('users.expiry', '>=', (new Carbon)->subSeconds(self::GRACE_PERIOD_SECONDS))
            ->orWhere('users.expiry', '=', new Carbon('1900-01-01'))
            ->orWhere('users.expiry', '=', null);
    }

    public function scopeOnlyCurrent(Builder $query): Builder
    {
        return $query
            ->where('expiry', '>=', (new Carbon)->subSeconds(self::GRACE_PERIOD_SECONDS))
            ->orWhere('expiry', '=', new Carbon('1900-01-01'))
            ->orWhere('expiry', '=', null);
    }

    public function scopeOfCmsManager(Builder $query): Builder
    {
        $user = Auth::user();
        if ($user->isA('super_admin') || $user->isA('super_super_admin')) {
            return $query;
        } else {
            return $query->whereDescendantOrSelf($user);
        }
    }

    public function scopeOfManager(Builder $query, $user): Builder
    {
        return $query->where('supervisor_id', $user->id);
    }

    public function scopeUnderDepartments(Builder $query, array $departments): Builder
    {
        (new UnderDepartmentsScope(new Collection($departments)))->apply($query, $this);
        return $query;
    }

    public function scopeInViewableRootDepartments($query, $key = 'users.department_id')
    {
        $user = Auth::user();
        if (!$user instanceof User || $user->can(Ability::VIEW_ALL_DEPARTMENTS)) return $query;
        (new UnderDepartmentsScope($user->getViewableRootDepartments(), $key))->apply($query, $this);
        return $query;
    }

    public function scopeCurrentUserViewable(Builder $query): Builder
    {
        $user = Auth::user();
        if (!$user instanceof User || $user->can(Ability::VIEW_ALL_DEPARTMENTS)) return $query;
        (new UnderDepartmentsOrViewableScope($user->getViewableRootDepartments()->merge($user->managing)))->apply($query, $this);
        return $query;
    }

    /**
     * This scope is mainly intended for filtering many-to-many relations such that only the relation for the specified
     * user is loaded
     *
     * @param Builder $builder
     * @param User $user
     * @return Builder
     */
    public function scopeOnlyUser(Builder $builder, User $user): Builder
    {
        return $builder->whereKey($user->getKey());
    }

    public function scopeWithPermission(Builder $query, HasAcl $item, $permission): Builder
    {
        assert($item instanceof Model);
        $grammar = $query->getQuery()->getGrammar();
        $wrapped_permission = $grammar->wrap($permission);
        $wrapped_user_acl_table = $grammar->wrapTable($item->userAccesses()->getTable());
        $wrapped_department_acl_table = $grammar->wrapTable($item->departmentAccesses()->getTable());
        $wrapped_item_table = $grammar->wrap($item->getTable());
        $wrapped_item_key_in_user_pivot = $grammar->wrap($item->userAccesses()->getQualifiedForeignPivotKeyName());
        $wrapped_item_key_in_department_pivot = $grammar->wrap($item->departmentAccesses()->getQualifiedForeignPivotKeyName());
        $wrapped_item_key = $grammar->wrap($item->getKeyName());

        return $query->whereRaw(
            /** @lang TSQL */
            <<< EOF
-- noinspection SqlSignature
coalesce(
    (
        select $wrapped_permission
        from users u
            left join $wrapped_user_acl_table on u.id = $wrapped_user_acl_table.user_id
        where $wrapped_item_key_in_user_pivot = ? and u.id = users.id
    )
    , (
        select top 1 first_value($wrapped_permission) over (order by _lft desc)
        from departments d
            left join $wrapped_department_acl_table on d.id = $wrapped_department_acl_table.department_id
        where $wrapped_item_key_in_department_pivot = ?
            and (
                select _rgt
                from departments
                where departments.id = users.department_id
            ) between d._lft and d._rgt
            and $wrapped_permission is not null
    )
    , (
        select $wrapped_permission from $wrapped_item_table where $wrapped_item_key = ?
    )
) <> 0
EOF
        )
            ->addBinding($item->getKey())
            ->addBinding($item->getKey())
            ->addBinding($item->getKey());
    }

    /////////////////
    //// Helpers ////
    /////////////////

    /**
     * @param string|null $app_id
     * @return Device|null
     */
    public function getCurrentDevice($app_id)
    {
        return $this->devices()->wherePivot('app_id', $app_id)->orderBy('device_user.updated_at', 'desc')->first();
    }

    /**
     * Update the user's icon or cover
     *
     * @param string $field
     * @param string $data
     */
    public function updateIconOrCover(string $field, string $data)
    {
        $storage_prefix = self::IMAGE_MAPPINGS[$field];
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mime = $finfo->buffer($data);
        if (!isset(self::SUPPORTED_MIME_TYPES[$mime])) {
            throw new UnsupportedMediaTypeHttpException('The format is not supported');
        }
        $name = Str::random(40) . '.' . self::SUPPORTED_MIME_TYPES[$mime];
        $new_path = $storage_prefix . '/' . $name;
        if (class_exists(Imagick::class)) {
            $image = new Imagick();
            $image->readImageBlob($data, $name);
            $image->scaleImage(
                $field === 'icon' ? config('ctf.thumbnail_width') : config('ctf.cover_width'),
                $field === 'icon' ? config('ctf.thumbnail_height') : config('ctf.cover_height'),
                true
            );
            $this->getStorage()->put($new_path, $image->getImageBlob());
        } else {
            $this->getStorage()->put($new_path, $data);
        }
        $this->deleteIconOrCover($field);
        $this->setAttribute($field, $name)->save();
    }

    /**
     * Delete the user's icon or cover
     *
     * @param string $field
     */
    public function deleteIconOrCover(string $field)
    {
        $storage_prefix = self::IMAGE_MAPPINGS[$field];
        nullable(
            $this->getAttribute($field),
            function (string $file_name) use ($field, $storage_prefix) {
                $this->setAttribute($field, null)->save();
                $this->getStorage()->delete("$storage_prefix/$file_name");
            }
        );
    }

    /**
     * Get the currently used shared secret from user
     *
     * @param string|null $app_id
     * @return string|null
     */
    public function getSharedSecret($app_id)
    {
        $shared_secret = $this->getFieldFromKey('shared_secret', $app_id);
        if ($shared_secret === null) {
            $this->doSomethingOnDeviceKey(
                function (DeviceKey $deviceKey) {
                    $deviceKey->shared_secret = app(KeyManager::class)
                        ->getSymmetricKeyForClient($deviceKey->key);
                    $deviceKey->save();
                },
                $app_id
            );
            $shared_secret = $this->getFieldFromKey('shared_secret', $app_id);
        }
        return $shared_secret;
    }

    /**
     * Get the currently used public key for the user
     *
     * @param string|null $app_id
     * @return PublicKeyInterface|null
     */
    public function getPublicKey($app_id)
    {
        return $this->getFieldFromKey('key', $app_id);
    }

    public function canViewDepartment(Department $department): bool
    {
        return $this->can('view', $department);
    }

    /**
     * Get the ID of specific version of enrolled course by user
     *
     * @param CourseMaster $course_master
     * @return int|null
     */
    public function getIdOfEnrolledCourseVersion(CourseMaster $course_master): ?int
    {
        return $this->courses()->where('course_master_id', '=', $course_master->id)->getBaseQuery()->value('id');
    }

    public function getHasher(): Hasher
    {
        if ($this->hasher === null) {
            $this->hasher = app(Hasher::class);
        }
        return $this->hasher;
    }

    public function setHasher(Hasher $hasher)
    {
        $this->hasher = $hasher;
    }

    public function hasPrivilege(int $privilege): bool
    {
        return ($this->privileges & $privilege) === $privilege;
    }

    public function addPrivilege(int $privilege)
    {
        $this->privileges |= $privilege;
    }

    public function removePrivilege(int $privilege)
    {
        $this->privileges &= ~$privilege;
    }

    public function getStorage(): Cloud
    {
        if ($this->storage === null) {
            $this->storage = app(Cloud::class);
        }
        return $this->storage;
    }

    public function setStorage(Cloud $storage)
    {
        $this->storage = $storage;
    }

    protected function getCtfMappings(): array
    {
        return app(UserMapper::class)->getCtfMappings();
    }

    protected function getCtfQueryBuilder(): QueryBuilder
    {
        return app(UserMapper::class)->getCtfQueryBuilder();
    }

    private function getFieldFromKey(string $field, string $app_id)
    {
        return $this->doSomethingOnDeviceKey(
            function (DeviceKey $deviceKey) use ($field) {
                return $deviceKey->getAttributeValue($field);
            },
            $app_id
        );
    }

    private function doSomethingOnDeviceKey(callable $callback, string $app_id)
    {
        return nullable(
            $this->getCurrentDevice($app_id),
            function (Device $device) use ($callback) {
                return nullable(
                    $device->getCurrentDeviceKey(),
                    $callback
                );
            }
        );
    }

    public function getPrivilegeStringsAttribute(): array
    {
        $result = [];
        /** @noinspection SuspiciousLoopInspection */
        for ($bit = 1; $bit > 0; $bit <<= 1) {
            if ($this->hasPrivilege($bit)) {
                $string = self::getPrivilegeString($bit);
                if (Str::startsWith($string, 'PRIVILEGE_')) {
                    $result[] = $string;
                }
            }
        }
        return $result;
    }

    public function getAbilitiesAttribute(): Collection
    {
        return $this->getAbilities();
    }

    public function getForbiddenAbilitiesAttribute(): Collection
    {
        return $this->getForbiddenAbilities();
    }

    public function getViewableRootDepartments()
    {
        $departments = $this->viewableDepartments;
        $extra =  $this->viewable_root_department_id;
        $self = $this->department;
        $extraDepartment = (new Department)->find($extra);
        foreach ([$self, $extraDepartment] as $added) {
            if ($added && !$departments->contains($added)) $departments->push($added);
        }
        return $departments;
    }

    public function getViewableRootDepartmentsAttribute()
    {
        return $this->getViewableRootDepartments();
    }

    public function getFullName(): string
    {
        return $this->full_name ?? $this->name;
    }

    public function getEnglishName(): string
    {
        return $this->name_en ?? $this->name;
    }

    public function getChineseName(): string
    {
        return $this->name_zh ?? $this->name;
    }

    public function getLocalisedName(): string
    {
        $language = Language::getAppLanguage();
        return strpos($language->id, 'zh') === 0 ? $this->getChineseName() : $this->getEnglishName();
    }

    /**
     * Return the field used as the "user name" in the sign in process.
     *
     * This field should be unique.
     *
     * @return string
     */
    public function getAuthUserName(): string
    {
        return (string)config('auth.user_name')[0];
    }

    /**
     * Return all the fields which can be used as the "user name" in the sign in process.
     *
     * These fields should be unique.
     *
     * @return array
     */
    public function getAuthUserNames(): array
    {
        return array_unique(config('auth.user_name'));
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function routeNotificationFor($driver)
    {
        switch ($driver) {
            case ApnPusher::class:
                return $this->channels()->where('channel_type', '=', 'apn');
            case BaiduPusher::class:
                return $this->channels()->where('channel_type', '=', 'baidu');
        }
        return $this->traitRouteNotificationFor($driver);
    }

    public function makeUserHistory(Carbon $from_date, Carbon $to_date): UserHistory
    {
        $result = new UserHistory();
        $result->user_id = $this->id;
        $result->name = $this->name;
        $result->title = $this->title;
        $result->unit = $this->unit;
        $result->department_id = $this->department_id;
        $result->from_date = $from_date;
        $result->to_date = $to_date;
        return $result;
    }

    public function isManagerOf(Department $department): bool
    {
        return $department->manager_id === $this->id || $this->managing()->whereKey($department->id)->count();
    }

    public function isIndirectSupervisorOf(User $user): bool
    {
        // check if user is subordinate of myself
        $supervisor = $user;
        while ($supervisor->id !== $this->id) {
            $supervisor = $supervisor->supervisor;
            if ($supervisor === null) {
                return false;
            }
        }
        return true;
    }

    public function preferredLocale()
    {
        return $this->language;
    }

    /**
     * Ecrypt the user's google_2fa secret.
     *
     * @param  string  $value
     * @return string
     */
    public function setGoogle2faSecretAttribute($value)
    {
        //  $this->attributes['google2fa_secret'] = encrypt($value);
        $this->attributes['google2fa_secret'] = $value;
    }

    /**
     * Decrypt the user's google_2fa secret.
     *
     * @param  string  $value
     * @return string
     */
    public function getGoogle2faSecretAttribute($value)
    {
        // return decrypt($value);
        return $value;
    }

    /**
     * add value to the ac_num
     *
     * @param int $value
     */
    public function increseAcnum($value)
    {
        $this->ac_num += $value;
    }

    /**
     * change the user password_reset to be true
     *
     * @param int $value
     */
    public function passwordIsReset()
    {
        $this->attributes['password_reset'] = 1;
    }

    public function getLftName()
    {
        return 'user_lft';
    }

    public function getRgtName()
    {
        return 'user_rgt';
    }

    public function getParentIdName()
    {
        return 'supervisor_id';
    }

    // ========================================
    // Custom Bouncer-like Permission Methods
    // ========================================

    /**
     * Get the user's roles relationship
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(
            Role::class,
            'assigned_roles',
            'entity_id',
            'role_id'
        )->where('entity_type', self::class);
    }

    /**
     * Get the user's abilities relationship
     */
    public function abilities(): BelongsToMany
    {
        return $this->belongsToMany(
            Ability::class,
            'permissions',
            'entity_id',
            'ability_id'
        )->where('entity_type', self::class)
         ->where('forbidden', false);
    }

    /**
     * Get the user's forbidden abilities relationship
     */
    public function forbiddenAbilities(): BelongsToMany
    {
        return $this->belongsToMany(
            Ability::class,
            'permissions',
            'entity_id',
            'ability_id'
        )->where('entity_type', self::class)
         ->where('forbidden', true);
    }

    /**
     * Get all abilities for this user (including from roles)
     */
    public function getAbilities(): Collection
    {
        // Get direct abilities
        $directAbilities = $this->abilities()->get();

        // Get abilities from roles
        $roleAbilities = collect();
        foreach ($this->roles as $role) {
            $roleAbilities = $roleAbilities->merge($role->abilities);
        }

        // Merge and remove duplicates
        return $directAbilities->merge($roleAbilities)->unique('id');
    }

    /**
     * Get all forbidden abilities for this user
     */
    public function getForbiddenAbilities(): Collection
    {
        return $this->forbiddenAbilities()->get();
    }

    /**
     * Check if user has a specific role
     */
    public function isA(string $role): bool
    {
        return $this->roles()->where('name', $role)->exists();
    }

    /**
     * Check if user can perform an ability
     */
    public function can($ability, $arguments = []): bool
    {
        // First check if it's forbidden
        if ($this->getForbiddenAbilities()->contains('name', $ability)) {
            return false;
        }

        // Then check if user has the ability
        return $this->getAbilities()->contains('name', $ability);
    }
}
