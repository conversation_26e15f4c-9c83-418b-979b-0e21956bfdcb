<?php
declare(strict_types = 1);

namespace App\Models\Learning;

use App\Database\QueryHelper;
use App\Models\Ability;
use App\Models\Department;
use App\Models\Pivots\PermissionPivot;
use App\Models\User;
use App\OverridingLibraries\Illuminate\Database\Eloquent\Model;
use App\OverridingLibraries\Silber\Bouncer\Database\Queries\Abilities;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\Grammars\SqlServerGrammar;
use function get_class;
use Carbon\Carbon;

/**
 * @property-read Collection $userAccesses
 * @property-read Collection $departmentAccesses
 * @property-read Collection $titleAccesses
 */
trait PermissionAccess
{
    public static function bootPermissionAccess()
    {
        static::made(
            function (Model $model) {
                $model->addOwnerships(['userAccesses', 'departmentAccesses', 'titleAccesses']);
                $model->casts += [
                    'create_permission' => 'bool',
                    'delete_permission' => 'bool',
                    'update_permission' => 'bool',
                    'read_permission' => 'bool',
                ];
            }
        );
    }

    public abstract static function getUserAclPivot() : PermissionPivot;
    public abstract static function getDepartmentAclPivot() : PermissionPivot;

    public function userAccesses() : BelongsToMany
    {
        return $this->belongsToMany(User::class, get_class(static::getUserAclPivot()));
    }

    public function departmentAccesses() : BelongsToMany
    {
        return $this->belongsToMany(Department::class, get_class(static::getDepartmentAclPivot()));
    }

    public function titleAccesses() : HasMany
    {
        return $this->hasMany("App\\Models\\Learning\\" . class_basename(self::class) . "TitlePermission");
    }


    public function hasPermission($permission, User $user) : bool
    {
        /* rule for permissions precedence:
         * 1. User permission
         * 2. Title or department permissions, from the child to department, if both are defined DENY takes precedence
         * 3. Default permissions
         */
        /** @var bool|null $user_permission */
        $user_permission = nullable(
            $this->userAccesses()->withoutGlobalScopes()->find($user->id),
            function (User $user) use ($permission) {
                $pivot = $user->pivot;
                assert($pivot instanceof PermissionPivot);
                return $pivot->hasPermission($permission);
            }
        );
        $departments = $user->department === null ? new Collection() : $user->department->getAncestors()->add($user->department)->reverse();
        $department_permission = $departments->reduce(
            function (?bool $carry, Department $current) use ($permission) {
                return $carry ?? nullable(
                    $this->departmentAccesses->find($current),
                        function (Department $department) use ($permission) {
                            return $department->pivot->hasPermission($permission);
                        }
                    );
            }
        );
        $title_date_permission = 0;
        $title_permission = $this->titleAccesses->reduce(
            function (?bool $carry, TitlePermission $model) use ($permission, $user, &$title_date_permission) {
                if($model->title_field == "entry_date") {
                    $filtered_date = Carbon::parse($model->title);
                    $user_date = Carbon::parse($user->entry_date);
                    if ($user_date->eq($filtered_date)) {
                        return self::coalescePermission($carry, $model->hasPermission($permission));
                    }
                }
                else if ($model->title_field == "entry_date_from") {
                    $filtered_date = Carbon::parse($model->title);
                    $user_date = Carbon::parse($user->entry_date);
                    if ($user_date->gte($filtered_date)) {
                        $title_date_permission++;
                        if( $title_date_permission >=2 ) {
                           return self::coalescePermission($carry, $model->hasPermission($permission));
                        }
                    }
                }
                else if($model->title_field == "entry_date_to") {
                    $filtered_date = Carbon::parse($model->title);
                    $user_date = Carbon::parse($user->entry_date);
                    if ($user_date->lte($filtered_date)) {
                        $title_date_permission++;
                        if( $title_date_permission >=2 ) {
                           return self::coalescePermission($carry, $model->hasPermission($permission));
                        }
                    }
                }
                else if ($user->getAttribute($model->title_field) === $model->title) {
                    return self::coalescePermission($carry, $model->hasPermission($permission));
                }
                return $carry;
            }
        );

        return $user_permission ?? self::coalescePermission($department_permission, $title_permission) ?? $this->getAttribute($permission) ?? false;
    }

    private static function coalescePermission(?bool $a, ?bool $b) : ?bool {
        return $a !== null && $b !== null ? $a && $b : ($a ?? $b);
    }

    /**
     * @param Builder $query
     * @param User|null $user
     * @param $permission
     * @return Builder|static
     */
    public function scopeWithPermission(Builder $query, User $user, $permission = PermissionPivot::READ) : Builder
    {
        if ($user->can('*') || $user->hasPrivilege(User::PRIVILEGE_MANAGE_COURSE_MATERIALS)) {
            return $query;
        }

        $with_title_permissions = [['id' => null, 'permission' => null]];
        Relation::noConstraints(
            function () use ($permission, $user, &$with_title_permissions) {
                $relation = $this->titleAccesses();
                $relation->each(
                    function (TitlePermission $model) use ($permission, $relation, $user, &$with_title_permissions) {
                        if($model->title_field == "entry_date") {
                            $filtered_date = Carbon::parse($model->title);
                            $user_date = Carbon::parse($user->entry_date);
                            if ($user_date->eq($filtered_date)) {
                                $with_title_permissions[] = [
                                    'id' => $model->getAttribute($relation->getForeignKeyName()),
                                    'permission' => $model->hasPermission($permission),
                                ];
                            }
                        }
                        else if ($model->title_field == "entry_date_from") {
                            $filtered_date = Carbon::parse($model->title);
                            $user_date = Carbon::parse($user->entry_date);
                            if ($user_date->gte($filtered_date)) {
                                $with_title_permissions[] = [
                                    'id' => $model->getAttribute($relation->getForeignKeyName()),
                                    'permission' => $model->hasPermission($permission),
                                ];
                            }
                            else {
                                info('entry date from no permission');
                                $with_title_permissions[] = [
                                    'id' => $model->getAttribute($relation->getForeignKeyName()),
                                    'permission' => 0,
                                ];
                            }
                        }
                        else if($model->title_field == "entry_date_to") {
                            $filtered_date = Carbon::parse($model->title);
                            $user_date = Carbon::parse($user->entry_date);
                            if ($user_date->lte($filtered_date)) {
                                $with_title_permissions[] = [
                                    'id' => $model->getAttribute($relation->getForeignKeyName()),
                                    'permission' => $model->hasPermission($permission),
                                ];
                            }
                            else {
                                $with_title_permissions[] = [
                                    'id' => $model->getAttribute($relation->getForeignKeyName()),
                                    'permission' => 0,
                                ];
                            }
                        }
                        if ($user->getAttribute($model->title_field) === $model->title) {
                            $with_title_permissions[] = [
                                'id' => $model->getAttribute($relation->getForeignKeyName()),
                                'permission' => $model->hasPermission($permission),
                            ];
                        }
                    }
                );
            }
        );

        $grammar = $query->getQuery()->getGrammar();
        $wrapped_permission = $grammar->wrap($permission);
        $wrapped_user_acl_table = $grammar->wrapTable($this->userAccesses()->getTable());
        $wrapped_department_acl_table = $grammar->wrapTable($this->departmentAccesses()->getTable());
        $wrapped_item_table = $grammar->wrapTable($this->getTable());
        $wrapped_item_key_in_user_pivot = $grammar->wrap($this->userAccesses()->getQualifiedForeignPivotKeyName());
        $wrapped_item_key_in_department_pivot = $grammar->wrap(
            $this->departmentAccesses()->getQualifiedForeignPivotKeyName()
        );
        $wrapped_item_key = $grammar->wrap($this->getQualifiedKeyName());
        $title_permission_builder = (new QueryHelper($query->getConnection()))->getQueryFromTableLiteral($with_title_permissions, 't');
        $title_permission_literal = $title_permission_builder->from;
        if ($title_permission_literal instanceof Expression) {
            $title_permission_literal = $title_permission_literal->getValue();
        }

        if ($grammar instanceof SqlServerGrammar) {
            $sql = /** @lang TSQL */ <<< EOF
(select coalesce(
    (
        select $wrapped_user_acl_table.$wrapped_permission
        from users u
            left join $wrapped_user_acl_table on u.id = $wrapped_user_acl_table.user_id
        where $wrapped_item_key_in_user_pivot = $wrapped_item_key and u.id = ?
    )
    , coalesce(
        (
            select top 1 $wrapped_department_acl_table.$wrapped_permission
            from departments d
                left join $wrapped_department_acl_table on d.id = $wrapped_department_acl_table.department_id
            where $wrapped_item_key_in_department_pivot = $wrapped_item_key
                and (
                    select _rgt
                    from users left join departments on departments.id = users.department_id
                    where users.id = ?
                ) between d._lft and d._rgt
                and $wrapped_department_acl_table.$wrapped_permission is not null
            order by _lft desc
        ) & (
            select min(t.permission)
            from $title_permission_literal
            where t.id = $wrapped_item_key
        )
        , (
            select top 1 $wrapped_department_acl_table.$wrapped_permission
            from departments d
                left join $wrapped_department_acl_table on d.id = $wrapped_department_acl_table.department_id
            where $wrapped_item_key_in_department_pivot = $wrapped_item_key
                and (
                    select _rgt
                    from users left join departments on departments.id = users.department_id
                    where users.id = ?
                ) between d._lft and d._rgt
                and $wrapped_department_acl_table.$wrapped_permission is not null
            order by _lft desc
        )
        , (
            select min(t.permission)
            from $title_permission_literal
            where t.id = $wrapped_item_key
        )
    )
    , $wrapped_item_table.$wrapped_permission
)) <> 0
EOF;
        } else {
            $sql = /** @lang MariaDB */ <<< EOF
(select coalesce(
    (
        select $wrapped_user_acl_table.$wrapped_permission
        from users u
            left join $wrapped_user_acl_table on u.id = $wrapped_user_acl_table.user_id
        where $wrapped_item_key_in_user_pivot = $wrapped_item_key and u.id = ?
    )
    , coalesce(
        (
            select $wrapped_department_acl_table.$wrapped_permission
            from departments d
                left join $wrapped_department_acl_table on d.id = $wrapped_department_acl_table.department_id
            where $wrapped_item_key_in_department_pivot = $wrapped_item_key
                and (
                    select _rgt
                    from users left join departments on departments.id = users.department_id
                    where users.id = ?
                ) between d._lft and d._rgt
                and $wrapped_department_acl_table.$wrapped_permission is not null
            order by _lft desc
            limit 1
        ) and (
            select min(t.permission)
            from $title_permission_literal
            where t.id = $wrapped_item_key
        )
        , (
            select $wrapped_department_acl_table.$wrapped_permission
            from departments d
                left join $wrapped_department_acl_table on d.id = $wrapped_department_acl_table.department_id
            where $wrapped_item_key_in_department_pivot = $wrapped_item_key
                and (
                    select _rgt
                    from users left join departments on departments.id = users.department_id
                    where users.id = ?
                ) between d._lft and d._rgt
                and $wrapped_department_acl_table.$wrapped_permission is not null
            order by _lft desc
            limit 1
        )
        , (
            select min(t.permission)
            from $title_permission_literal
            where t.id = $wrapped_item_key
        )
    )
    , $wrapped_item_table.$wrapped_permission
))
EOF;

        }
        return $query->whereRaw($sql)
            ->addBinding($user->getKey())
            ->addBinding($user->getKey())
            ->addBinding($title_permission_builder->getBindings())
            ->addBinding($user->getKey())
            ->addBinding($title_permission_builder->getBindings())
            ->orWhereIn(
                'id',
                Ability::select('entity_id')
                    ->where('entity_type', '=', static::class)
                    ->where('name', '=', $permission)
                    ->whereIn('abilities.id', (new Abilities())->getQueryForAuthority($user)->select('id'))
            );
    }
}
