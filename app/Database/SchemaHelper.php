<?php
declare(strict_types=1);

namespace App\Database;

use App\Exceptions\NotSupportedException;
use Illuminate\Database\Connection;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;
use Illuminate\Database\Schema\Grammars\Grammar;
use Illuminate\Database\SqlServerConnection;

class SchemaHelper
{
    /**
     * @var Builder
     */
    private $builder;

    public function __construct(Builder $builder)
    {
        $this->builder = $builder;
    }

    /**
     * Get the default index name
     *
     * @param $table_name
     * @param $type
     * @param array $columns
     * @return string
     */
    public static function createIndexName($table_name, $type, array $columns) : string
    {
        // Create a dummy connection for the Blueprint constructor
        $connection = app('db.connection');
        $blueprint = new class($connection, $table_name) extends Blueprint
        {
            public function createIndexName($type, array $columns) : string
            {
                return parent::createIndexName($type, $columns);
            }
        };
        return $blueprint->createIndexName($type, $columns);
    }

    /**
     * Add a unique index with columns containing possible NULL values
     *
     * This is to circumvent SQL server limitation that NULL are included in unique indexes
     *
     * @param string $table_name
     * @param array $columns
     * @param string|null $index_name
     */
    public function addUniqueIndexWithNull(string $table_name, array $columns, string $index_name = null) : void
    {
        $this->builder->table(
            $table_name,
            function (Blueprint $table) use ($columns, $index_name) {
                $table_name = $table->getTable();
                $index_name = $index_name ?? $this->createIndexName($table_name, 'unique', $columns);
                if ($this->builder->getConnection() instanceof SqlServerConnection) {
                    $grammar = $this->builder->getConnection()->getSchemaGrammar();
                    $this->builder->getConnection()->unprepared(
                        sprintf(/** @lang TSQL */'create unique index %s on %s (%s) where %s',
                            $grammar->wrap($index_name),
                            $grammar->wrapTable($table_name),
                            implode(', ', array_map([$grammar, 'wrap'], $columns)),
                            implode(
                                ' and ',
                                array_map(
                                    function (string $column) use ($grammar) : string {
                                        return "{$grammar->wrap($column)} is not null";
                                    },
                                    $columns
                                )
                            )
                        )
                    );
                } else {
                    $table->unique($columns, $index_name);
                }
            }
        );
    }

    public function dropDefaultConstraint(string $table_name, string $column) : void {
        $connection = $this->getConnection();
        if ($connection instanceof SqlServerConnection) {
            $constraint = $connection->selectOne(
                sprintf(
                /** @lang TSQL */
                    <<< 'EOF'
select object_name([default_object_id]) as name
from sys.columns
where [object_id] = object_id('[dbo].%s') and [name] = ?
EOF
                    ,
                    $this->getGrammar()->wrapTable($table_name)
                ),
                [$column]
            );
            if (!empty($constraint['name'])) {
                $connection->unprepared(
                    /** @lang TSQL */ <<< EOF
alter table [dbo].{$this->getGrammar()->wrapTable($table_name)}
drop constraint {$this->getGrammar()->wrap($constraint['name'])}
EOF
                );
            }
        } else {
            $connection->unprepared(
                <<< EOF
alter table {$this->getGrammar()->wrapTable($table_name)} alter {$this->getGrammar()->wrap($column)} drop default
EOF
            );
        }
    }

    /**
     * Drop a primary key on the table (if exists)
     *
     * @param string $table_name
     */
    public function dropPrimaryKey(string $table_name) : void
    {
        $connection = $this->getConnection();
        $key = $this->getPrimaryKeyName($table_name);
        if ($key !== null) {
            if ($connection instanceof SqlServerConnection) {
                $connection->unprepared(
                /** @lang TSQL */
                    <<< EOF
alter table {$this->getGrammar()->wrapTable($table_name)}
drop constraint {$this->getGrammar()->wrap($key)}
EOF
                );
            } else {
                $connection->unprepared(/** @lang MariaDB */"alter table {$this->getGrammar()->wrapTable($table_name)} drop primary key");
            }
        }
    }

    public function getPrimaryKeyName(string $table_name) : ?string
    {
        $connection = $this->getConnection();
        if ($connection instanceof SqlServerConnection) {
            return $connection->selectOne(
                /** @lang TSQL */<<< EOF
select name
from sys.key_constraints
where [type] = 'PK' and [parent_object_id] = object_id('[dbo].{$this->getGrammar()->wrapTable($table_name)}')
EOF
            )['name'] ?? null;
        } else {
            return $connection->selectOne(
                /** @lang MySQL */<<< EOF
show index from {$this->getGrammar()->wrap($table_name)} where key_name = 'PRIMARY'
EOF
            )['Key_name'] ?? null;
        }
    }

    /**
     * Get the names of all check constraints on a particular table and column
     *
     * @param string $table_name
     * @param string|null $column If not specified return all constraints on that table
     * @return array
     */
    public function getCheckConstraintNames(string $table_name, ?string $column) : array
    {
        $connection = $this->getConnection();
        if ($connection instanceof SqlServerConnection) {
            $query = $connection->table('information_schema.constraint_column_usage')
                ->join(
                    'information_schema.check_constraints',
                    'constraint_column_usage.constraint_name',
                    '=',
                    'check_constraints.constraint_name'
                )
                ->where('table_name', '=', $table_name);
            if ($column !== null) {
                $query->where('column_name', '=', $column);
            }
        } else {
            if ($column !== null) {
                throw new NotSupportedException('Getting check constraints depending on column is not supported on MySQL');
            }
            $query = $connection->table('information_schema.check_constraints')
                ->where('table_name', '=', $table_name);
        }
        return $query->pluck('check_constraints.constraint_name')->all();
    }

    public function dropConstraint(string $table_name, string $constraint_name)
    {
        $connection = $this->getConnection();
        $grammar = $connection->getSchemaGrammar();
        $connection->unprepared(
            /** @lang TSQL */
            "alter table {$grammar->wrapTable($table_name)} drop constraint {$grammar->wrap($constraint_name)}"
        );
    }

    public function getBuilder() : Builder
    {
        return $this->builder;
    }

    public function getConnection() : Connection
    {
        return $this->getBuilder()->getConnection();
    }

    public function getGrammar() : Grammar
    {
        return $this->getBuilder()->getConnection()->getSchemaGrammar();
    }
}
