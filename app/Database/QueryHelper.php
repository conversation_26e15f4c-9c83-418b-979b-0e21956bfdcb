<?php
declare(strict_types=1);

namespace App\Database;

use App\Exceptions\NotSupportedException;
use App\Helpers\InfoOutputter;
use Countable;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Database\MySqlConnection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\Grammars\Grammar;
use Illuminate\Database\Query\Grammars\MySqlGrammar;
use Illuminate\Database\Query\Grammars\SqlServerGrammar;
use Illuminate\Database\SqlServerConnection;
use Illuminate\Support\Arr;
use Symfony\Component\Console\Output\OutputInterface;
use function array_diff;
use function array_filter;
use function array_keys;
use function array_map;
use function array_merge_array;
use function array_unique;
use function array_values;
use function collect;
use function implode;
use function sprintf;

class QueryHelper
{
    public const FUNCTION_NAMES
        = [
            'call' => [
                MySqlGrammar::class => 'call',
                SqlServerGrammar::class => 'exec',
            ],
            'last_day' => [
                MySqlGrammar::class => 'last_day',
                SqlServerGrammar::class => 'eomonth',
            ],
            'ifnull' => [
                MySqlGrammar::class => 'ifnull',
                SqlServerGrammar::class => 'isnull',
            ],
            'now' => [
                MySqlGrammar::class => 'now',
                SqlServerGrammar::class => 'getdate',
            ],
            'if' => [
                MySqlGrammar::class => 'if',
                SqlServerGrammar::class => 'iif',
            ],
            'sum' => [
                MySqlGrammar::class => 'sum',
                SqlServerGrammar::class => 'sum',
            ],
        ];

    /**
     * @var ConnectionInterface
     */
    private $connection;

    public function __construct(ConnectionInterface $connection)
    {
        $this->connection = $connection;
    }

    public function getConnection() : ConnectionInterface
    {
        return $this->connection;
    }

    public function getGrammar() : Grammar
    {
        return $this->connection->table('test')->getGrammar();
    }

    /**
     * Get the function name equivalent to the supplied MySQL function used for this connection
     *
     * @param string $mysql_function_name
     * @return string
     */
    public function getFunctionName(string $mysql_function_name) : string
    {
        $data = self::FUNCTION_NAMES;
        foreach ($data[$mysql_function_name] as $class => $result) {
            if ($this->getGrammar() instanceof $class) {
                return $result;
            }
        }
        throw new NotSupportedException(
            sprintf(
                "The function for $mysql_function_name is not known on %s.",
                class_basename($this->getGrammar())
            )
        );
    }

    public function addSchemaOnSqlsrv(string $identifier, string $schema = 'dbo') : string
    {
        if ($this->getConnection() instanceof SqlServerConnection) {
            return "$schema.$identifier";
        } else {
            return $identifier;
        }
    }

    /**
     * Get a query builder containing the supplied data as the "from" source
     *
     * @param array $values The data in key => value form
     * @param string $alias The name used as the table alias
     * @return Builder
     */
    public function getQueryFromTableLiteral(
        array $values,
        string $alias = 'data'
    ) : Builder
    {
        $connection = $this->getConnection();
        $grammar = $this->getGrammar();
        $columns_array = array_unique(array_merge_array(array_map('array_keys', $values)));
        $columns = $grammar->columnize($columns_array);
        $wrapped_alias = $grammar->wrapTable($alias);
        if ($values === []) {
            $sql = "(select 0 = 1) as $wrapped_alias";
        } elseif ($connection instanceof SqlServerConnection) {
            $parameters = collect($values)->map(
                function ($record) use ($columns_array, $grammar) {
                    $real_record = array_map(
                        static function ($key) use ($record) {
                            return $record[$key] ?? null;
                        }
                        , $columns_array
                    );
                    return '(' . $grammar->parameterize($real_record) . ')';
                }
            )
                ->implode(', ');
            $sql = "(values $parameters) as $wrapped_alias($columns)";
        } else {
            $select_statement = collect($values)->map(
                function ($record) use ($grammar) {
                    $parameters = implode(
                        ', ',
                        array_map(
                            function ($key, $argument) use ($grammar) {
                                return "{$grammar->parameter($argument)} as {$grammar->wrap($key)}";
                            },
                            array_keys($record),
                            array_values($record)
                        )
                    );
                    return "select $parameters";
                }
            )
                ->implode(' union ');
            $sql = "($select_statement) as $wrapped_alias";
        }
        return $connection->query()->fromRaw(
            $sql,
            array_values(
                array_filter(
                    Arr::flatten($values, 1),
                    function ($value) {
                        return !($value instanceof Expression);
                    }
                )
            )
        );
    }

    /**
     * Get a raw expression
     *
     * @param mixed $column
     * @return Expression
     */
    public function raw($column) : Expression
    {
        return $this->getConnection()->raw($column);
    }

    /**
     * Apply the given database function onto the given column
     *
     * @param string $function
     * @param string|Expression ...$arguments
     * @return Expression
     */
    public function applyFunction(string $function, ...$arguments) : Expression
    {
        return $this->getConnection()->raw(
            sprintf('%s(%s)', $function, implode(', ', array_map([$this->getGrammar(), 'wrap'], $arguments)))
        );
    }

    /**
     * Apply the equivalent database function of the given MySQL function onto the given column
     *
     * @param string $function
     * @param string|Expression ...$arguments
     * @return Expression
     */
    public function applyMysqlFunction(string $function, ...$arguments) : Expression
    {
        return $this->applyFunction($this->getFunctionName($function), ...$arguments);
    }

    /**
     * Evaluate an expression
     *
     * @param string|Expression $expression
     * @param array $bindings
     * @return mixed
     */
    public function evaluate($expression, array $bindings = []) {
        return $this->getConnection()->selectOne(
            sprintf('select (%s) as result', $expression),
            $bindings
        )['result'];
    }

    /**
     * Get an expression for start of month
     *
     * @param string|Expression $column
     * @return Expression
     */
    public function getStartOfMonth($column) : Expression
    {
        if ($this->getConnection() instanceof SqlServerConnection) {
            return $this->getConnection()->raw(
                sprintf('dateadd(month, datediff(month, 0, %s), 0)', $this->getGrammar()->wrap($column))
            );
        } else {
            return $this->getConnection()->raw(
                sprintf(
                    'date_add(last_day(date_sub(%s, interval 1 month)), interval 1 day)',
                    $this->getGrammar()->wrap($column)
                )
            );
        }
    }

    /**
     * Get an expression for end of month
     *
     * @param string|Expression $column
     * @return Expression
     */
    public function getEndOfMonth($column) : Expression
    {
        return $this->applyMysqlFunction('last_day', $column);
    }

    /**
     * Get a statement for inserting or updating a table from matching records
     *
     * This is not fully supported on MySQL as merge is not available, insert on duplicate key update is used instead
     *
     * @param string $into The table to be inserted or updated
     * @param Builder $select The data source
     * @param array $update_columns The columns to be updated into existing rows
     * @param array $insert_columns The columns to be inserted into new rows
     * @param array $key The unique key on the target table to identify matching rows
     * @param bool $delete_not_matched If non-matching rows are to be deleted
     *
     * @return string
     */
    public function getMergeSql(
        string $into,
        Builder $select,
        array $update_columns,
        array $insert_columns,
        array $key,
        bool $delete_not_matched = false
    ) : string
    {
        if ($delete_not_matched && $this->getConnection() instanceof MySqlConnection) {
            throw new NotSupportedException('$delete_not_matched is not supported on MySQL');
        }
        $grammar = $this->getGrammar();
        $wrapped_into = $grammar->wrapTable($into);
        $select_sql = $select->toSql();
        $equal_mapper = function ($column_name) use ($into, $grammar) {
            return sprintf(
                '%s = %s',
                $grammar->wrap("$into.$column_name"),
                $grammar->wrap("source.$column_name")
            );
        };
        /** @noinspection PhpUnusedLocalVariableInspection This is actually used */
        $conditions = implode(' and ', array_map($equal_mapper, $key));
        $update_string = implode(', ', array_map($equal_mapper, $update_columns));
        $insert_values_string = implode(
            ', ',
            array_map(
                function ($x) use ($grammar) {
                    return $grammar->wrap("source.$x");
                },
                $insert_columns
            )
        );
        $insert_columns_string = implode(', ', array_map([$grammar, 'wrap'], $insert_columns));
        return $this->getConnection() instanceof MySqlConnection
            ? /** @lang MySQL */<<< EOF
insert into $wrapped_into ($insert_columns_string)
select $insert_values_string from ($select_sql) source
on duplicate key update $update_string;
EOF
            : /** @lang TSQL */<<< EOF
merge $wrapped_into
using ($select_sql) as source
on ($conditions)
when matched then update set $update_string
when not matched by target then insert ($insert_columns_string) values ($insert_values_string)
EOF
            . ($delete_not_matched ? "\nwhen not matched by source then delete" : '') . ';';
    }

    public function massUpdate(
        string $into
        , array $data
        , array $key
    ) : void {
        $grammar = $this->getGrammar();
        $wrapped_into = $grammar->wrapTable($into);
        $equal_mapper = function ($column_name) use ($into, $grammar) {
            return sprintf(
                '%s = %s',
                $grammar->wrap("$into.$column_name"),
                $grammar->wrap("source.$column_name")
            );
        };
        $this->chunkProcess(
            $data,
            function (array $chunked) use ($equal_mapper, $key, $into, $wrapped_into) : void {
                if ($chunked !== []) {
                    $source = $this->getQueryFromTableLiteral($chunked);
                    $select_sql = $source->toSql();
                    $columns = array_unique(array_merge_array(array_map('array_keys', $chunked)));
                    $update_string = implode(', ', array_map($equal_mapper, array_diff($columns, $key)));
                    $conditions = implode(' and ', array_map($equal_mapper, $key));
                    $sql = $this->getConnection() instanceof SqlServerConnection
                        ? /** @lang TSQL */ <<< EOF
update $wrapped_into
    set $update_string
    from $wrapped_into inner join ($select_sql) as source on $conditions
EOF
                        : /** @lang MariaDB */ <<< EOF
update $wrapped_into
    inner join ($select_sql) as source on $conditions
    set $update_string
EOF;
                    $this->getConnection()->statement($sql, $source->getBindings());
                }
            }
        );
    }

    /**
     * Mass insert or update
     *
     * @param string $into Target table to be inserted or updated
     * @param array $data The raw data
     * @param array $key The key columns to identify existing rows
     * @param array|null $update_columns The columns to be updated in existing rows, default to all but created_at
     * @param array|null $insert_columns The columns to be inserted in new rows, default to all
     * @param bool $identity_insert If false exclude the key columns from the default in update_columns and insert_columns
     */
    public function insertOrUpdate(
        string $into
        , array $data
        , array $key
        , array $update_columns = null
        , array $insert_columns = null
        , bool $identity_insert = true
    ) : void {
        $columns = array_unique(array_merge_array(array_map('array_keys', $data)));
        $this->chunkProcess(
            $data,
            function (array $chunked) use ($columns, $key, $identity_insert, $into) : void {
                if ($chunked !== []) {
                    $source = $this->getQueryFromTableLiteral($chunked);
                    $sql = $this->getMergeSql(
                        $into,
                        $source,
                        $update_columns ?? array_diff($columns, ['created_at'], $identity_insert ? [] : $key),
                        $insert_columns ?? array_diff($columns, $identity_insert ? [] : $key),
                        $key
                    );
                    $this->getConnection()->statement($sql, $source->getBindings());
                }
            }
        );
    }

    /**
     * Copy columns from a table to another
     *
     * @param array $columns
     * @param string $old_table
     * @param string $new_table
     */
    public function copyColumns(array $columns, string $old_table, string $new_table) : void
    {
        $mappings = array_map_assoc(
            function ($key, $value) {
                return is_int($key) ? [$value => $value] : [$key => $value];
            },
            $columns
        );
        $this->getConnection()->table($new_table)->insertUsing(
            array_keys($mappings),
            $this->getConnection()->table($old_table)->select(array_values($mappings))
        );
    }

    /**
     * Process data in chunk to prevent exceeding PDO parameter count
     * @param array $data
     * @param callable $callback
     * @param InfoOutputter|null $infoOutputter
     * @return array The result of $callback for each chunk
     */
    public function chunkProcess(array $data, callable $callback, InfoOutputter $infoOutputter = null) : array
    {
        $total = count($data);
        $result = [];
        if ($total) {
            $pdo_limit = $this->getConnection() instanceof MySqlConnection
                ? $this->connection->selectOne(
                    /** @lang MySQL */
                    'select @@max_prepared_stmt_count as count'
                )['count']
                // FIXME: hardcoded number, any other way to query this number?
                : ($this->getConnection() instanceof SqlServerConnection ? 2100 : 1000);
            $first = Arr::first($data);
            $column_count = is_array($first) || $first instanceof Countable ? count($first) : 1;
            $chunk_size = intdiv($pdo_limit - 100, $column_count);
            $infoOutputter = $infoOutputter ?? app(InfoOutputter::class);
            $infoOutputter->output(
                "<info>Splitting $total rows with $chunk_size in each chunk.</info>",
                OutputInterface::VERBOSITY_VERY_VERBOSE
            );
            $chunks = array_chunk($data, $chunk_size);
            foreach ($chunks as $i => $piece) {
                $result[] = $callback($piece);
                $infoOutputter->output(
                    "<info>Processed " . ($i + 1) . " out of " . count($chunks) . " chunks.</info>",
                    OutputInterface::VERBOSITY_VERY_VERBOSE
                );
            }
        }
        return $result;
    }

    /**
     * Execute a stored procedure in a database
     *
     * @param string $procedure_name
     * @param mixed ...$arguments
     */
    public function executeStoredProcedure(string $procedure_name, ...$arguments) : void
    {
        $grammar = $this->getGrammar();
        $this->getConnection()->statement(
            sprintf(
                $grammar instanceof SqlServerGrammar ? 'exec %s %s' : 'call %s(%s)',
                $grammar->wrap($procedure_name),
                $grammar->parameterize($arguments)
            ),
            array_values(
                array_filter(
                    $arguments,
                    function ($item) use ($grammar) {
                        return !$grammar->isExpression($item);
                    }
                )
            )
        );
    }

    /**
     * Create a new table from the result of a given query
     *
     * @param Builder $query
     * @param string $new_table
     */
    public function selectIntoNewTable(Builder $query, string $new_table)
    {
        if ($this->getConnection() instanceof SqlServerConnection) {
            $this->getConnection()->statement(
                /** @lang TSQL */
                <<< EOF
select * into {$query->getGrammar()->wrapTable($new_table)}
from ({$query->toSql()}) query
EOF
                , $query->getBindings()
            );
        } else {
            $this->getConnection()->statement(
                <<< EOF
create table {$query->getGrammar()->wrapTable($new_table)}
engine = InnoDB
as {$query->toSql()}
EOF

                , $query->getBindings()
            );
        }
    }

    public function addAliasIntoExpression(Expression $raw, string $alias) : Expression {
        return new Expression(sprintf('(%s) as %s', $raw->__toString(), $alias));
    }
}
