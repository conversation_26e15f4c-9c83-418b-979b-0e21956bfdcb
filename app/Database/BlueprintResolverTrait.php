<?php
declare(strict_types=1);
/**
 * Created by IntelliJ IDEA.
 * User: michael
 * Date: 15/03/19
 * Time: 12:02
 */

namespace App\Database;

use App\OverridingLibraries\Illuminate\Database\Schema\Blueprint;
use Closure;
use Illuminate\Database\Connection;

trait BlueprintResolverTrait
{

    public function __construct(Connection $connection)
    {
        parent::__construct($connection);
        $this->blueprintResolver(
            function (Connection $connection, string $table, ?Closure $callback = null) {
                return new Blueprint($connection, $table, $callback);
            }
        );
    }
}
