# Laravel API Testing Setup

This directory contains a comprehensive testing setup for the Laravel API with Docker Compose and smoke testing.

## Quick Start

Run the complete test suite:

```bash
./run-tests.sh
```

## Test Components

### 1. Docker Compose Test Environment (`docker-compose.test.yml`)

A complete testing environment including:
- **MySQL 8.0** database with health checks
- **Redis** for caching and queues
- **Laravel Web Application** with health monitoring
- **Migration Service** for database setup
- **Smoke Test Service** for endpoint testing
- **Health Check Service** for system monitoring

### 2. Smoke Tests (`scripts/smoke-test.sh`)

Tests critical API endpoints:
- ✅ Health check endpoint (`/api/ping/health`)
- ✅ School events endpoint (`/api/school_events/all?page=1&per_page=10`)
- ✅ External staging environment comparison
- ✅ Basic API structure validation

### 3. Health Checks (`scripts/health-check.sh`)

Comprehensive system health monitoring:
- ✅ Service availability
- ✅ Database connectivity
- ✅ Application error detection
- ✅ Performance monitoring

### 4. Test Runner (`run-tests.sh`)

Orchestrates the entire testing process:
- Prerequisites checking
- Service startup and health monitoring
- Database migrations
- Test execution
- Results reporting
- Cleanup

## Usage Examples

### Run All Tests
```bash
./run-tests.sh
```

### Run Individual Components
```bash
# Check prerequisites only
./run-tests.sh prerequisites

# Start services only
./run-tests.sh start

# Run smoke tests only
./run-tests.sh smoke

# Run health checks only
./run-tests.sh health

# Show service logs
./run-tests.sh logs

# Clean up resources
./run-tests.sh cleanup
```

### Manual Docker Compose Commands
```bash
# Start all services
docker-compose -f docker-compose.test.yml up -d

# Run smoke tests
docker-compose -f docker-compose.test.yml run --rm smoke-test

# Run health checks
docker-compose -f docker-compose.test.yml run --rm health-check

# View logs
docker-compose -f docker-compose.test.yml logs web

# Stop and clean up
docker-compose -f docker-compose.test.yml down -v
```

## Configuration

### Environment Variables

Key environment variables in `.env.test`:
- `APP_ENV=testing`
- `APP_DEBUG=true`
- `CTF_ENCRYPTION=false`
- `DB_HOST=database`
- `REDIS_HOST=redis`

### Test Endpoints

The smoke tests target these endpoints:
1. **Health Check**: `/api/ping/health`
2. **School Events**: `/api/school_events/all?page=1&per_page=10`
3. **External Staging**: `https://mcne-staging.iclass.hk/api/school_events/all?page=1&per_page=10`

## Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check Docker status
   docker info
   
   # View service logs
   ./run-tests.sh logs
   ```

2. **Database connection issues**
   ```bash
   # Check database health
   docker-compose -f docker-compose.test.yml exec database mysqladmin ping
   ```

3. **Health check failures**
   ```bash
   # Test health endpoint manually
   curl http://localhost:8080/api/ping/health
   ```

### Service Dependencies

Services start in this order:
1. Database (MySQL)
2. Redis
3. Web Application
4. Migration Service
5. Test Services

### Ports

- **Web Application**: 8080
- **Database**: 3306
- **Redis**: 6379

## Test Results

The test suite provides detailed results:
- ✅ **PASS**: Test completed successfully
- ⚠️ **WARN**: Test completed with warnings
- ❌ **FAIL**: Test failed

### Success Criteria

All tests pass when:
- All services are healthy
- Database migrations complete
- Health endpoint returns 200
- School events endpoint returns valid data
- No critical errors detected

## Integration with CI/CD

This setup is designed for CI/CD integration:

```yaml
# Example GitHub Actions
- name: Run API Tests
  run: |
    chmod +x run-tests.sh
    ./run-tests.sh
```

## Files Overview

- `docker-compose.test.yml` - Complete test environment
- `scripts/smoke-test.sh` - Endpoint testing
- `scripts/health-check.sh` - System health monitoring
- `scripts/mysql-init.sql` - Database initialization
- `run-tests.sh` - Test orchestration
- `.env.test` - Test environment configuration
- `TESTING.md` - This documentation
